.  
├── Assistantmodules/  
│ ├── assistant-bar.html  
│ ├── assistant-bar.js  
│ ├── assistant.css  
│ ├── assistant.html  
│ └── assistant.js  
├── Groupmodules/  
│ ├── groupchat.js  
│ └── grouprenderer.js  
├── Musicmodules/  
│ ├── music.css  
│ ├── music.html  
│ └── music.js  
├── Notemodules/  
│ ├── notes.css  
│ ├── notes.html  
│ └── notes.js  
├── Translatormodules/  
│ ├── translator.css  
│ ├── translator.html  
│ └── translator.js  
├── VCPDistributedServer/  
│ ├── Plugin/  
│ │ ├── FileOperator/  
│ │ │ ├── .env  
│ │ │ ├── .env.example  
│ │ │ ├── FileOperator.js  
│ │ │ └── plugin-manifest.json  
│ │ ├── MusicController/  
│ │ │ ├── music-controller.js  
│ │ │ └── plugin-manifest.json  
│ │ └── VCPEverything/  
│ │ ├── .env  
│ │ ├── local-search-controller.js  
│ │ └── plugin-manifest.json  
│ ├── Plugin.js  
│ └── VCPDistributedServer.js  
├── assets/  
│ ├── E1.jpg  
│ ├── E2.jpg  
│ ├── E3.jpg  
│ ├── E4.jpg  
│ ├── E5.jpg  
│ ├── E6.jpg  
│ ├── E7.jpg  
│ ├── E7.png  
│ ├── dark.jpg  
│ ├── default\_avatar.png  
│ ├── default\_group\_avatar.png  
│ ├── default\_user\_avatar.png  
│ ├── font/  
│ │ └── MavenPro-ExtraBold.ttf  
│ ├── icon.png  
│ ├── icon4.png  
│ ├── light.jpeg  
│ ├── music-note.svg  
│ ├── musicdark.jpeg  
│ ├── musiclight.jpeg  
│ ├── repeat-one.svg  
│ ├── repeat.svg  
│ ├── sakuranight.png  
│ ├── shuffle.svg  
│ └── svg/  
│ ├── PaperAirplane.svg  
│ └── PaperClip.svg  
├── modules/  
│ ├── chatManager.js  
│ ├── fileManager.js  
│ ├── image-viewer.html  
│ ├── image-viewer.js  
│ ├── inputEnhancer.js  
│ ├── ipc/  
│ │ ├── agentHandlers.js  
│ │ ├── chatHandlers.js  
│ │ ├── fileDialogHandlers.js  
│ │ ├── groupChatHandlers.js  
│ │ ├── settingsHandlers.js  
│ │ └── windowHandlers.js  
│ ├── itemListManager.js  
│ ├── messageRenderer.js  
│ ├── musicScannerWorker.js  
│ ├── notificationRenderer.js  
│ ├── renderer/  
│ │ ├── colorUtils.js  
│ │ ├── contentProcessor.js  
│ │ ├── domBuilder.js  
│ │ ├── imageHandler.js  
│ │ ├── messageContextMenu.js  
│ │ └── streamManager.js  
│ ├── settingsManager.js  
│ ├── text-viewer.html  
│ ├── text-viewer.js  
│ ├── topicListManager.js  
│ ├── topicSummarizer.js  
│ ├── ui-helpers.js  
│ └── uiManager.js  
├── styles/  
│ ├── animations.css  
│ ├── base.css  
│ ├── chat.css  
│ ├── components.css  
│ ├── layout.css  
│ ├── messageRenderer.css  
│ ├── notifications.css  
│ ├── settings.css  
│ ├── themes.css  
│ └── theme黑白.css  
├── .npmrc  
├── LICENSE  
├── README-VCP.md  
├── README.md  
├── list\_files\_script.py  
├── main.html  
├── main.js  
├── package-lock.json  
├── package.json  
├── preload.js  
├── renderer.js  
├── run\_silent.vbs  
├── start.bat  
├── style.css  
└── vchat开发小助手(开发可以先看我哦).md