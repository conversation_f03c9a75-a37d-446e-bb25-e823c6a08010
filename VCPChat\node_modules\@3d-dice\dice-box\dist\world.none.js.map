{"version": 3, "file": "world.none.js", "sources": ["../src/components/world.none.js"], "sourcesContent": ["import Dice from './Dice'\nclass WorldNone {\n\tconfig\n\t#canvas\n\tinitialized = false\n\t#dieCache = {}\n\t#count = 0\n\t#sleeperCount = 0\n\t#dieRollTimer = []\n\t#rollCompleteTimer\n\tnoop = () => {}\n\tconstructor(options){\n\t\tthis.onInitComplete = options.onInitComplete || this.noop\n\t\tthis.onThemeLoaded = options.onThemeLoaded || this.noop\n\t\tthis.onRollResult = options.onRollResult || this.noop\n\t\tthis.onRollComplete = options.onRollComplete || this.noop\n\t\tthis.onDieRemoved = options.onDieRemoved || this.noop\n\t\tthis.initialized = this.initScene(options)\n\t}\n\n\tasync initScene(config) {\n\t\t// set the config from World\n\t\tthis.config = config.options\n\n\t\t// init complete - let the world know\n\t\tthis.onInitComplete()\n\t}\n\n\tresize(){\n\t\t\n\t}\n\n\tloadTheme(){\n\t\treturn Promise.resolve()\n\t}\n\n\tupdateConfig(options){\n\t\tObject.assign(this.config, options)\n\t}\n\n\taddNonDie(die){\n\t\tconsole.log('die', die)\n\t\tclearTimeout(this.#rollCompleteTimer)\n\t\tconst {id, value, ...rest} = die\n\t\tconst newDie = {\n\t\t\tid,\n\t\t\tvalue,\n\t\t\tconfig: rest\n\t\t}\n\t\tthis.#dieCache[id] = newDie\n\t\t\n\t\tthis.#dieRollTimer.push(setTimeout(() => {\n\t\t\tthis.handleAsleep(newDie)\n\t\t}, this.#count++ * this.config.delay))\n\n\t\t// since we don't have a render loop, we'll set an internal timer\n\t\tthis.#rollCompleteTimer = setTimeout(() => {\n\t\t\tthis.onRollComplete()\n\t\t}, 500)\n\t}\n\n\tadd(die){\n\t\tconsole.log(\"add die\")\n\t\tthis.addNonDie(die)\n\t}\n\n\tremove(data){\n\t\tconsole.log(\"remove die\")\n\t\t// TODO: test this with exploding dice\n\t\tconst dieData = this.#dieCache[data.id]\n\t\t\n\t\t// check if this is d100 and remove associated d10 first\n\t\tif(dieData.hasOwnProperty('d10Instance')){\n\t\t\t// delete entry\n\t\t\tdelete this.#dieCache[dieData.d10Instance.id]\n\t\t\t// decrement count\n\t\t\tthis.#sleeperCount--\n\t\t}\n\n\t\t// delete entry\n\t\tdelete this.#dieCache[data.id]\n\t\t// decrement count\n\t\tthis.#sleeperCount--\n\n\t\tthis.onDieRemoved(data.rollId)\n\t}\n\n\tclear(){\n\t\tif(!Object.keys(this.#dieCache).length && !this.#sleeperCount) {\n\t\t\treturn\n\t\t}\n\n\t\tthis.#dieRollTimer.forEach(timer=>clearTimeout(timer))\n\n\t\t// remove all dice\n\t\tObject.values(this.#dieCache).forEach(die => {\n\t\t\tif(die.mesh)\n\t\t\t\tdie.mesh.dispose()\n\t\t})\n\t\t\n\t\t// reset storage\n\t\tthis.#dieCache = {}\n\t\tthis.#count = 0\n\t\tthis.#sleeperCount = 0\n\t}\n\n\t// handle the position updates from the physics worker. It's a simple flat array of numbers for quick and easy transfer\n\tasync handleAsleep(die){\n\t\t// mark this die as asleep\n\t\tdie.asleep = true\n\t\n\t\t// get the roll result for this die\n\t\tawait Dice.getRollResult(die)\n\t\n\t\tif(die.d10Instance || die.dieParent) {\n\t\t\t// if one of the pair is asleep and the other isn't then it falls through without getting the roll result\n\t\t\t// otherwise both dice in the d100 are asleep and ready to calc their roll result\n\t\t\tif(die?.d10Instance?.asleep || die?.dieParent?.asleep) {\n\t\t\t\tconst d100 = die.config.sides === 100 ? die : die.dieParent\n\t\t\t\tconst d10 = die.config.sides === 10 ? die : die.d10Instance\n\t\t\t\tif (d10.value === 0 && d100.value === 0) {\n\t\t\t\t\td100.value = 100; // 00 + 0 is 100 on a d100\n\t\t\t\t} else {\n\t\t\t\t\td100.value = d100.value + d10.value\n\t\t\t\t}\n\t\n\t\t\t\tthis.onRollResult({\n\t\t\t\t\trollId: d100.config.rollId,\n\t\t\t\t\tvalue : d100.value\n\t\t\t\t})\n\t\t\t}\n\t\t} else {\n\t\t\t// turn 0's on a d10 into a 10\n\t\t\tif(die.config.sides === 10 && die.value === 0) {\n\t\t\t\tdie.value = 10\n\t\t\t}\n\t\t\tthis.onRollResult({\n\t\t\t\trollId: die.config.rollId,\n\t\t\t\tvalue: die.value\n\t\t\t})\n\t\t}\n\t\t// add to the sleeper count\n\t\tthis.#sleeperCount++\n\t}\n}\n\nexport default WorldNone"], "names": ["WorldNone", "options", "__publicField", "__privateAdd", "_canvas", "_dieCache", "_count", "_sleeperCount", "_dieRollTimer", "_rollCompleteTimer", "config", "die", "__privateGet", "id", "value", "rest", "new<PERSON>ie", "__privateWrapper", "__privateSet", "data", "dieData", "timer", "<PERSON><PERSON>", "_a", "_b", "d100", "d10"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,MAAMA,EAAU;AAAA,EAUf,YAAYC,GAAQ;AATpB,IAAAC,EAAA;AACA,IAAAC,EAAA,MAAAC,GAAA;AACA,IAAAF,EAAA,qBAAc;AACd,IAAAC,EAAA,MAAAE,GAAY,CAAE;AACd,IAAAF,EAAA,MAAAG,GAAS;AACT,IAAAH,EAAA,MAAAI,GAAgB;AAChB,IAAAJ,EAAA,MAAAK,GAAgB,CAAE;AAClB,IAAAL,EAAA,MAAAM,GAAA;AACA,IAAAP,EAAA,cAAO,MAAM;AAAA,IAAE;AAEd,SAAK,iBAAiBD,EAAQ,kBAAkB,KAAK,MACrD,KAAK,gBAAgBA,EAAQ,iBAAiB,KAAK,MACnD,KAAK,eAAeA,EAAQ,gBAAgB,KAAK,MACjD,KAAK,iBAAiBA,EAAQ,kBAAkB,KAAK,MACrD,KAAK,eAAeA,EAAQ,gBAAgB,KAAK,MACjD,KAAK,cAAc,KAAK,UAAUA,CAAO;AAAA,EACzC;AAAA,EAED,MAAM,UAAUS,GAAQ;AAEvB,SAAK,SAASA,EAAO,SAGrB,KAAK,eAAgB;AAAA,EACrB;AAAA,EAED,SAAQ;AAAA,EAEP;AAAA,EAED,YAAW;AACV,WAAO,QAAQ,QAAS;AAAA,EACxB;AAAA,EAED,aAAaT,GAAQ;AACpB,WAAO,OAAO,KAAK,QAAQA,CAAO;AAAA,EAClC;AAAA,EAED,UAAUU,GAAI;AACb,YAAQ,IAAI,OAAOA,CAAG,GACtB,aAAaC,EAAA,MAAKH,EAAkB;AACpC,UAAM,EAAC,IAAAI,GAAI,OAAAC,GAAO,GAAGC,EAAI,IAAIJ,GACvBK,IAAS;AAAA,MACd,IAAAH;AAAA,MACA,OAAAC;AAAA,MACA,QAAQC;AAAA,IACR;AACD,IAAAH,EAAA,MAAKP,GAAUQ,CAAE,IAAIG,GAErBJ,EAAA,MAAKJ,GAAc,KAAK,WAAW,MAAM;AACxC,WAAK,aAAaQ,CAAM;AAAA,IAC3B,GAAKC,EAAA,MAAKX,GAAL,MAAgB,KAAK,OAAO,KAAK,CAAC,GAGrCY,EAAA,MAAKT,GAAqB,WAAW,MAAM;AAC1C,WAAK,eAAgB;AAAA,IACrB,GAAE,GAAG;AAAA,EACN;AAAA,EAED,IAAIE,GAAI;AACP,YAAQ,IAAI,SAAS,GACrB,KAAK,UAAUA,CAAG;AAAA,EAClB;AAAA,EAED,OAAOQ,GAAK;AACX,YAAQ,IAAI,YAAY;AAExB,UAAMC,IAAUR,EAAA,MAAKP,GAAUc,EAAK,EAAE;AAGtC,IAAGC,EAAQ,eAAe,aAAa,MAEtC,OAAOR,EAAA,MAAKP,GAAUe,EAAQ,YAAY,EAAE,GAE5CH,EAAA,MAAKV,GAAL,MAID,OAAOK,EAAA,MAAKP,GAAUc,EAAK,EAAE,GAE7BF,EAAA,MAAKV,GAAL,KAEA,KAAK,aAAaY,EAAK,MAAM;AAAA,EAC7B;AAAA,EAED,QAAO;AACN,IAAG,CAAC,OAAO,KAAKP,EAAA,MAAKP,EAAS,EAAE,UAAU,CAACO,EAAA,MAAKL,OAIhDK,EAAA,MAAKJ,GAAc,QAAQ,CAAAa,MAAO,aAAaA,CAAK,CAAC,GAGrD,OAAO,OAAOT,EAAA,MAAKP,EAAS,EAAE,QAAQ,CAAAM,MAAO;AAC5C,MAAGA,EAAI,QACNA,EAAI,KAAK,QAAS;AAAA,IACtB,CAAG,GAGDO,EAAA,MAAKb,GAAY,CAAE,IACnBa,EAAA,MAAKZ,GAAS,IACdY,EAAA,MAAKX,GAAgB;AAAA,EACrB;AAAA;AAAA,EAGD,MAAM,aAAaI,GAAI;;AAOtB,QALAA,EAAI,SAAS,IAGb,MAAMW,EAAK,cAAcX,CAAG,GAEzBA,EAAI,eAAeA,EAAI;AAGzB,WAAGY,IAAAZ,KAAA,gBAAAA,EAAK,gBAAL,QAAAY,EAAkB,WAAUC,IAAAb,KAAA,gBAAAA,EAAK,cAAL,QAAAa,EAAgB,QAAQ;AACtD,cAAMC,IAAOd,EAAI,OAAO,UAAU,MAAMA,IAAMA,EAAI,WAC5Ce,IAAMf,EAAI,OAAO,UAAU,KAAKA,IAAMA,EAAI;AAChD,QAAIe,EAAI,UAAU,KAAKD,EAAK,UAAU,IACrCA,EAAK,QAAQ,MAEbA,EAAK,QAAQA,EAAK,QAAQC,EAAI,OAG/B,KAAK,aAAa;AAAA,UACjB,QAAQD,EAAK,OAAO;AAAA,UACpB,OAAQA,EAAK;AAAA,QAClB,CAAK;AAAA,MACD;AAAA;AAGD,MAAGd,EAAI,OAAO,UAAU,MAAMA,EAAI,UAAU,MAC3CA,EAAI,QAAQ,KAEb,KAAK,aAAa;AAAA,QACjB,QAAQA,EAAI,OAAO;AAAA,QACnB,OAAOA,EAAI;AAAA,MACf,CAAI;AAGF,IAAAM,EAAA,MAAKV,GAAL;AAAA,EACA;AACF;AA7ICH,IAAA,eAEAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA,eACAC,IAAA;"}