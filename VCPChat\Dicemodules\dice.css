/* 导入主应用的 themes.css 以使用共享的颜色变量 */
@import url('../styles/themes.css');

html,
body {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    overflow: hidden;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    position: relative;
    /* 使用主应用的背景变量 */
    background-color: var(--primary-bg);
    color: var(--primary-text);
}

#dice-canvas-container {
    position: absolute;
    top: 70px; /* 增加顶部空间以避免与标题重叠 */
    bottom: 80px; /* 为UI控件留出空间 */
    left: 10px;
    right: 10px;
    border-radius: 10px;
    /* 使用主题定义的面板背景，并添加磨砂玻璃效果 */
    background-color: var(--panel-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px); /* 兼容 Safari */
    box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color); /* 添加边框以增强视觉效果 */
}

#dice-canvas-container canvas {
    width: 100%;
    height: 100%;
    border-radius: 10px; /* 确保canvas也符合容器的圆角 */
}

.dice-ui {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    padding: 10px;
    background-color: var(--panel-bg);
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid var(--border-color);
    z-index: 10;
}

#notation-input {
    background-color: var(--input-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 8px;
    font-size: 16px;
}

#roll-button {
    background-color: var(--button-bg);
    color: var(--text-on-accent);
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

#roll-button:hover {
    background-color: var(--button-hover-bg);
}

h1 {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--highlight-text);
    text-shadow: var(--panel-text-shadow);
    z-index: 10;
    margin: 0;
    padding: 5px 15px;
    background-color: var(--panel-bg);
    border-radius: 8px;
}