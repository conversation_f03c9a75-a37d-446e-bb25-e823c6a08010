{"version": 3, "file": "audioEngine.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Audio/audioEngine.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAE5D,8CAA8C;AAC9C,MAAM,CAAC,kBAAkB,GAAG,CACxB,WAAkC,EAClC,YAAoC,EACpC,gBAAkF,EACpF,EAAE;IACA,OAAO,IAAI,WAAW,CAAC,WAAW,EAAE,YAAY,EAAE,gBAAgB,CAAC,CAAC;AACxE,CAAC,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,WAAW;IAyDpB;;OAEG;IACH,IAAW,YAAY;QACnB,IAAI,CAAC,IAAI,CAAC,wBAAwB,EAAE;YAChC,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;gBACrC,IAAI,CAAC,kBAAkB,EAAE,CAAC;aAC7B;SACJ;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAID;;;;;;;;OAQG;IACH,YACI,cAAqC,IAAI,EACzC,eAAuC,IAAI,EAC3C,mBAAqF,IAAI;QApFrF,kBAAa,GAA2B,IAAI,CAAC;QAC7C,6BAAwB,GAAG,KAAK,CAAC;QACjC,gBAAW,GAAgC,IAAI,CAAC;QAEhD,sBAAiB,GAAqE,IAAI,CAAC;QAEnG;;WAEG;QACI,mBAAc,GAAY,KAAK,CAAC;QAOvC;;;WAGG;QACH,gEAAgE;QACzD,8BAAyB,GAAY,KAAK,CAAC;QAElD;;WAEG;QACI,mBAAc,GAAY,KAAK,CAAC;QAEvC;;WAEG;QACI,mBAAc,GAAY,KAAK,CAAC;QAEvC;;;;WAIG;QACI,aAAQ,GAAY,IAAI,CAAC;QAEhC;;;WAGG;QACI,4BAAuB,GAAY,KAAK,CAAC;QAEhD;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAElE;;WAEG;QACI,4BAAuB,GAAG,IAAI,UAAU,EAAgB,CAAC;QAkHxD,cAAS,GAAG,KAAK,CAAC;QAgFlB,cAAS,GAAG,GAAG,EAAE;YACrB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,CAAC;QApKE,IAAI,CAAC,mBAAmB,EAAE,EAAE;YACxB,OAAO;SACV;QACD,IAAI,OAAO,MAAM,CAAC,YAAY,KAAK,WAAW,EAAE;YAC5C,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;QAED,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAClD,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,CAAC;QAE1C,IAAI;YACA,IACI,SAAS;gBACT,CAAC,CAAC,SAAS,CAAC,WAAW;gBACvB,CAAC,SAAS,CAAC,WAAW,CAAC,0BAA0B,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,IAAI,SAAS,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,EACnI;gBACE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC9B;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,yCAAyC;SAC5C;QAED,IAAI;YACA,IAAI,SAAS,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,CAAC,4BAA4B,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;aAC9B;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,yCAAyC;SAC5C;IACL,CAAC;IAED;;;OAGG;IACI,IAAI;QACP,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,MAAM;QACT,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAChC,CAAC;IAEO,mBAAmB;QACvB,IAAI,MAAqB,CAAC;QAC1B,IAAI,IAAI,CAAC,aAAc,CAAC,MAAM,KAAK,SAAS,EAAE;YAC1C,MAAM,GAAG,IAAI,CAAC,aAAc,CAAC,MAAM,EAAE,CAAC;SACzC;QACD,OAAO,MAAO,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;IACxC,CAAC;IAEO,uBAAuB;QAC3B,IAAI;YACA,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;oBACrB,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,EAAE,CAAC;iBAC3C;gBACD,mCAAmC;gBACnC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;gBAClD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;gBAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBACzB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC;iBAC3D;gBACD,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC;gBACrC,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,KAAK,SAAS,EAAE;oBACxC,yCAAyC;oBACzC,IAAI,CAAC,oBAAoB,EAAE,CAAC;iBAC/B;aACJ;SACJ;QAAC,OAAO,CAAC,EAAE;YACR,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,MAAM,CAAC,KAAK,CAAC,aAAa,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC;SAC3C;IACL,CAAC;IAGO,oBAAoB;QACxB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,OAAO;SACV;QACD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,IAAI,CAAC,mBAAmB,EAAE;aACrB,IAAI,CAAC,GAAG,EAAE;YACP,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,IAAI,CAAC,WAAW,EAAE;gBAClB,IAAI,CAAC,eAAe,EAAE,CAAC;aAC1B;YACD,wDAAwD;YACxD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACzD,CAAC,CAAC;aACD,KAAK,CAAC,GAAG,EAAE;YACR,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAC1B,CAAC,CAAC,CAAC;IACX,CAAC;IAEO,sBAAsB;QAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,uBAAuB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAEO,kBAAkB;QACtB,IAAI,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClD,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAsB,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACvE,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,mBAAmB,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,sBAAsB,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,QAAQ,CAAC;QAClC,MAAM,QAAQ,GAAG,CAAC,MAAM,CAAC,aAAa;YAClC,CAAC,CAAC,4CAA4C;YAC9C,CAAC,CAAC,onBAAonB,CAAC;QAE3nB,MAAM,GAAG,GACL,yJAAyJ;YACzJ,QAAQ;YACR,4UAA4U,CAAC;QAEjV,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;QAC9C,KAAK,CAAC,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC;QAChD,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAE5D,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAE5C,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAC7B,UAAU,EACV,GAAG,EAAE;YACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EACD,IAAI,CACP,CAAC;QACF,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAC7B,OAAO,EACP,GAAG,EAAE;YACD,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAChC,CAAC,EACD,IAAI,CACP,CAAC;QAEF,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACtD,CAAC;IAEO,oBAAoB;QACxB,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,WAAW,EAAE;YACvC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,GAAG,EAAE,GAAG,IAAI,CAAC;YACrE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC;SAC1E;IACL,CAAC;IAMO,eAAe;QACnB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC5C,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACtD,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,aAAa,EAAE;gBAC/C,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;gBAC1C,IAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,CAAC;gBAClC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;gBAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;gBACxD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;aAClC;YACD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;SAClC;QACD,IAAI,CAAC,yBAAyB,GAAG,KAAK,CAAC;QACvC,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAErD,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,CAAC;IACzC,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACtD,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC;SACrC;aAAM;YACH,OAAO,CAAC,CAAC,CAAC;SACb;IACL,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,SAAiB;QACpC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACtD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SAC1C;IACL,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,QAAkB;QACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;SAC7C;QACD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,aAAa,EAAE;YAC5E,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;YACnC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC;YAC7B,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;SAC9F;IACL,CAAC;CACJ", "sourcesContent": ["import type { Analyser } from \"./analyser\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport { Logger } from \"../Misc/logger\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { IAudioEngine } from \"./Interfaces/IAudioEngine\";\r\nimport { IsWindowObjectExist } from \"../Misc/domManagement\";\r\n\r\n// Sets the default audio engine to Babylon.js\r\nEngine.AudioEngineFactory = (\r\n    hostElement: Nullable<HTMLElement>,\r\n    audioContext: Nullable<AudioContext>,\r\n    audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode>\r\n) => {\r\n    return new AudioEngine(hostElement, audioContext, audioDestination);\r\n};\r\n\r\n/**\r\n * This represents the default audio engine used in babylon.\r\n * It is responsible to play, synchronize and analyse sounds throughout the  application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport class AudioEngine implements IAudioEngine {\r\n    private _audioContext: Nullable<AudioContext> = null;\r\n    private _audioContextInitialized = false;\r\n    private _muteButton: Nullable<HTMLButtonElement> = null;\r\n    private _hostElement: Nullable<HTMLElement>;\r\n    private _audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode> = null;\r\n\r\n    /**\r\n     * Gets whether the current host supports Web Audio and thus could create AudioContexts.\r\n     */\r\n    public canUseWebAudio: boolean = false;\r\n\r\n    /**\r\n     * The master gain node defines the global audio volume of your audio engine.\r\n     */\r\n    public masterGain: GainNode;\r\n\r\n    /**\r\n     * Defines if Babylon should emit a warning if WebAudio is not supported.\r\n     * @ignoreNaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    public WarnedWebAudioUnsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not mp3 are supported by your browser.\r\n     */\r\n    public isMP3supported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether or not ogg are supported by your browser.\r\n     */\r\n    public isOGGsupported: boolean = false;\r\n\r\n    /**\r\n     * Gets whether audio has been unlocked on the device.\r\n     * Some Browsers have strong restrictions about Audio and won t autoplay unless\r\n     * a user interaction has happened.\r\n     */\r\n    public unlocked: boolean = true;\r\n\r\n    /**\r\n     * Defines if the audio engine relies on a custom unlocked button.\r\n     * In this case, the embedded button will not be displayed.\r\n     */\r\n    public useCustomUnlockedButton: boolean = false;\r\n\r\n    /**\r\n     * Event raised when audio has been unlocked on the browser.\r\n     */\r\n    public onAudioUnlockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /**\r\n     * Event raised when audio has been locked on the browser.\r\n     */\r\n    public onAudioLockedObservable = new Observable<IAudioEngine>();\r\n\r\n    /**\r\n     * Gets the current AudioContext if available.\r\n     */\r\n    public get audioContext(): Nullable<AudioContext> {\r\n        if (!this._audioContextInitialized) {\r\n            this._initializeAudioContext();\r\n        } else {\r\n            if (!this.unlocked && !this._muteButton) {\r\n                this._displayMuteButton();\r\n            }\r\n        }\r\n        return this._audioContext;\r\n    }\r\n\r\n    private _connectedAnalyser: Nullable<Analyser>;\r\n\r\n    /**\r\n     * Instantiates a new audio engine.\r\n     *\r\n     * There should be only one per page as some browsers restrict the number\r\n     * of audio contexts you can create.\r\n     * @param hostElement defines the host element where to display the mute icon if necessary\r\n     * @param audioContext defines the audio context to be used by the audio engine\r\n     * @param audioDestination defines the audio destination node to be used by audio engine\r\n     */\r\n    constructor(\r\n        hostElement: Nullable<HTMLElement> = null,\r\n        audioContext: Nullable<AudioContext> = null,\r\n        audioDestination: Nullable<AudioDestinationNode | MediaStreamAudioDestinationNode> = null\r\n    ) {\r\n        if (!IsWindowObjectExist()) {\r\n            return;\r\n        }\r\n        if (typeof window.AudioContext !== \"undefined\") {\r\n            this.canUseWebAudio = true;\r\n        }\r\n\r\n        const audioElem = document.createElement(\"audio\");\r\n        this._hostElement = hostElement;\r\n        this._audioContext = audioContext;\r\n        this._audioDestination = audioDestination;\r\n\r\n        try {\r\n            if (\r\n                audioElem &&\r\n                !!audioElem.canPlayType &&\r\n                (audioElem.canPlayType('audio/mpeg; codecs=\"mp3\"').replace(/^no$/, \"\") || audioElem.canPlayType(\"audio/mp3\").replace(/^no$/, \"\"))\r\n            ) {\r\n                this.isMP3supported = true;\r\n            }\r\n        } catch (e) {\r\n            // protect error during capability check.\r\n        }\r\n\r\n        try {\r\n            if (audioElem && !!audioElem.canPlayType && audioElem.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/, \"\")) {\r\n                this.isOGGsupported = true;\r\n            }\r\n        } catch (e) {\r\n            // protect error during capability check.\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Flags the audio engine in Locked state.\r\n     * This happens due to new browser policies preventing audio to autoplay.\r\n     */\r\n    public lock() {\r\n        this._triggerSuspendedState();\r\n    }\r\n\r\n    /**\r\n     * Unlocks the audio engine once a user action has been done on the dom.\r\n     * This is helpful to resume play once browser policies have been satisfied.\r\n     */\r\n    public unlock() {\r\n        this._triggerRunningState();\r\n    }\r\n\r\n    private _resumeAudioContext(): Promise<void> {\r\n        let result: Promise<void>;\r\n        if (this._audioContext!.resume !== undefined) {\r\n            result = this._audioContext!.resume();\r\n        }\r\n        return result! || Promise.resolve();\r\n    }\r\n\r\n    private _initializeAudioContext() {\r\n        try {\r\n            if (this.canUseWebAudio) {\r\n                if (!this._audioContext) {\r\n                    this._audioContext = new AudioContext();\r\n                }\r\n                // create a global volume gain node\r\n                this.masterGain = this._audioContext.createGain();\r\n                this.masterGain.gain.value = 1;\r\n                if (!this._audioDestination) {\r\n                    this._audioDestination = this._audioContext.destination;\r\n                }\r\n                this.masterGain.connect(this._audioDestination);\r\n                this._audioContextInitialized = true;\r\n                if (this._audioContext.state === \"running\") {\r\n                    // Do not wait for the promise to unlock.\r\n                    this._triggerRunningState();\r\n                }\r\n            }\r\n        } catch (e) {\r\n            this.canUseWebAudio = false;\r\n            Logger.Error(\"Web Audio: \" + e.message);\r\n        }\r\n    }\r\n\r\n    private _tryToRun = false;\r\n    private _triggerRunningState() {\r\n        if (this._tryToRun) {\r\n            return;\r\n        }\r\n        this._tryToRun = true;\r\n\r\n        this._resumeAudioContext()\r\n            .then(() => {\r\n                this._tryToRun = false;\r\n                if (this._muteButton) {\r\n                    this._hideMuteButton();\r\n                }\r\n                // Notify users that the audio stack is unlocked/unmuted\r\n                this.unlocked = true;\r\n                this.onAudioUnlockedObservable.notifyObservers(this);\r\n            })\r\n            .catch(() => {\r\n                this._tryToRun = false;\r\n                this.unlocked = false;\r\n            });\r\n    }\r\n\r\n    private _triggerSuspendedState() {\r\n        this.unlocked = false;\r\n        this.onAudioLockedObservable.notifyObservers(this);\r\n        this._displayMuteButton();\r\n    }\r\n\r\n    private _displayMuteButton() {\r\n        if (this.useCustomUnlockedButton || this._muteButton) {\r\n            return;\r\n        }\r\n\r\n        this._muteButton = <HTMLButtonElement>document.createElement(\"BUTTON\");\r\n        this._muteButton.className = \"babylonUnmuteIcon\";\r\n        this._muteButton.id = \"babylonUnmuteIconBtn\";\r\n        this._muteButton.title = \"Unmute\";\r\n        const imageUrl = !window.SVGSVGElement\r\n            ? \"https://cdn.babylonjs.com/Assets/audio.png\"\r\n            : \"data:image/svg+xml;charset=UTF-8,%3Csvg%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%2239%22%20height%3D%2232%22%20viewBox%3D%220%200%2039%2032%22%3E%3Cpath%20fill%3D%22white%22%20d%3D%22M9.625%2018.938l-0.031%200.016h-4.953q-0.016%200-0.031-0.016v-12.453q0-0.016%200.031-0.016h4.953q0.031%200%200.031%200.016v12.453zM12.125%207.688l8.719-8.703v27.453l-8.719-8.719-0.016-0.047v-9.938zM23.359%207.875l1.406-1.406%204.219%204.203%204.203-4.203%201.422%201.406-4.219%204.219%204.219%204.203-1.484%201.359-4.141-4.156-4.219%204.219-1.406-1.422%204.219-4.203z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E\";\r\n\r\n        const css =\r\n            \".babylonUnmuteIcon { position: absolute; left: 20px; top: 20px; height: 40px; width: 60px; background-color: rgba(51,51,51,0.7); background-image: url(\" +\r\n            imageUrl +\r\n            \");  background-size: 80%; background-repeat:no-repeat; background-position: center; background-position-y: 4px; border: none; outline: none; transition: transform 0.125s ease-out; cursor: pointer; z-index: 9999; } .babylonUnmuteIcon:hover { transform: scale(1.05) } .babylonUnmuteIcon:active { background-color: rgba(51,51,51,1) }\";\r\n\r\n        const style = document.createElement(\"style\");\r\n        style.appendChild(document.createTextNode(css));\r\n        document.getElementsByTagName(\"head\")[0].appendChild(style);\r\n\r\n        document.body.appendChild(this._muteButton);\r\n\r\n        this._moveButtonToTopLeft();\r\n\r\n        this._muteButton.addEventListener(\r\n            \"touchend\",\r\n            () => {\r\n                this._triggerRunningState();\r\n            },\r\n            true\r\n        );\r\n        this._muteButton.addEventListener(\r\n            \"click\",\r\n            () => {\r\n                this._triggerRunningState();\r\n            },\r\n            true\r\n        );\r\n\r\n        window.addEventListener(\"resize\", this._onResize);\r\n    }\r\n\r\n    private _moveButtonToTopLeft() {\r\n        if (this._hostElement && this._muteButton) {\r\n            this._muteButton.style.top = this._hostElement.offsetTop + 20 + \"px\";\r\n            this._muteButton.style.left = this._hostElement.offsetLeft + 20 + \"px\";\r\n        }\r\n    }\r\n\r\n    private _onResize = () => {\r\n        this._moveButtonToTopLeft();\r\n    };\r\n\r\n    private _hideMuteButton() {\r\n        if (this._muteButton) {\r\n            document.body.removeChild(this._muteButton);\r\n            this._muteButton = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Destroy and release the resources associated with the audio context.\r\n     */\r\n    public dispose(): void {\r\n        if (this.canUseWebAudio && this._audioContextInitialized) {\r\n            if (this._connectedAnalyser && this._audioContext) {\r\n                this._connectedAnalyser.stopDebugCanvas();\r\n                this._connectedAnalyser.dispose();\r\n                this.masterGain.disconnect();\r\n                this.masterGain.connect(this._audioContext.destination);\r\n                this._connectedAnalyser = null;\r\n            }\r\n            this.masterGain.gain.value = 1;\r\n        }\r\n        this.WarnedWebAudioUnsupported = false;\r\n        this._hideMuteButton();\r\n        window.removeEventListener(\"resize\", this._onResize);\r\n\r\n        this.onAudioUnlockedObservable.clear();\r\n        this.onAudioLockedObservable.clear();\r\n    }\r\n\r\n    /**\r\n     * Gets the global volume sets on the master gain.\r\n     * @returns the global volume if set or -1 otherwise\r\n     */\r\n    public getGlobalVolume(): number {\r\n        if (this.canUseWebAudio && this._audioContextInitialized) {\r\n            return this.masterGain.gain.value;\r\n        } else {\r\n            return -1;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Sets the global volume of your experience (sets on the master gain).\r\n     * @param newVolume Defines the new global volume of the application\r\n     */\r\n    public setGlobalVolume(newVolume: number): void {\r\n        if (this.canUseWebAudio && this._audioContextInitialized) {\r\n            this.masterGain.gain.value = newVolume;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect the audio engine to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    public connectToAnalyser(analyser: Analyser): void {\r\n        if (this._connectedAnalyser) {\r\n            this._connectedAnalyser.stopDebugCanvas();\r\n        }\r\n        if (this.canUseWebAudio && this._audioContextInitialized && this._audioContext) {\r\n            this._connectedAnalyser = analyser;\r\n            this.masterGain.disconnect();\r\n            this._connectedAnalyser.connectAudioNodes(this.masterGain, this._audioContext.destination);\r\n        }\r\n    }\r\n}\r\n"]}