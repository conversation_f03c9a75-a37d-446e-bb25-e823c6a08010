/**
 * This represents the main contract an easing function should follow.
 * Easing functions are used throughout the animation system.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export interface IEasingFunction {
    /**
     * Given an input gradient between 0 and 1, this returns the corresponding value
     * of the easing function.
     * The link below provides some of the most common examples of easing functions.
     * @see https://easings.net/
     * @param gradient Defines the value between 0 and 1 we want the easing value for
     * @returns the corresponding value on the curve defined by the easing function
     */
    ease(gradient: number): number;
}
/**
 * Base class used for every default easing function.
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class EasingFunction implements IEasingFunction {
    /**
     * Interpolation follows the mathematical formula associated with the easing function.
     */
    static readonly EASINGMODE_EASEIN = 0;
    /**
     * Interpolation follows 100% interpolation minus the output of the formula associated with the easing function.
     */
    static readonly EASINGMODE_EASEOUT = 1;
    /**
     * Interpolation uses EaseIn for the first half of the animation and EaseOut for the second half.
     */
    static readonly EASINGMODE_EASEINOUT = 2;
    private _easingMode;
    /**
     * Sets the easing mode of the current function.
     * @param easingMode Defines the willing mode (EASINGMODE_EASEIN, EASINGMODE_EASEOUT or EASINGMODE_EASEINOUT)
     */
    setEasingMode(easingMode: number): void;
    /**
     * Gets the current easing mode.
     * @returns the easing mode
     */
    getEasingMode(): number;
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
    /**
     * Given an input gradient between 0 and 1, this returns the corresponding value
     * of the easing function.
     * @param gradient Defines the value between 0 and 1 we want the easing value for
     * @returns the corresponding value on the curve defined by the easing function
     */
    ease(gradient: number): number;
}
/**
 * Easing function with a circle shape (see link below).
 * @see https://easings.net/#easeInCirc
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class CircleEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a ease back shape (see link below).
 * @see https://easings.net/#easeInBack
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class BackEase extends EasingFunction implements IEasingFunction {
    /** Defines the amplitude of the function */
    amplitude: number;
    /**
     * Instantiates a back ease easing
     * @see https://easings.net/#easeInBack
     * @param amplitude Defines the amplitude of the function
     */
    constructor(
    /** Defines the amplitude of the function */
    amplitude?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a bouncing shape (see link below).
 * @see https://easings.net/#easeInBounce
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class BounceEase extends EasingFunction implements IEasingFunction {
    /** Defines the number of bounces */
    bounces: number;
    /** Defines the amplitude of the bounce */
    bounciness: number;
    /**
     * Instantiates a bounce easing
     * @see https://easings.net/#easeInBounce
     * @param bounces Defines the number of bounces
     * @param bounciness Defines the amplitude of the bounce
     */
    constructor(
    /** Defines the number of bounces */
    bounces?: number, 
    /** Defines the amplitude of the bounce */
    bounciness?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a power of 3 shape (see link below).
 * @see https://easings.net/#easeInCubic
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class CubicEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with an elastic shape (see link below).
 * @see https://easings.net/#easeInElastic
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class ElasticEase extends EasingFunction implements IEasingFunction {
    /** Defines the number of oscillations*/
    oscillations: number;
    /** Defines the amplitude of the oscillations*/
    springiness: number;
    /**
     * Instantiates an elastic easing function
     * @see https://easings.net/#easeInElastic
     * @param oscillations Defines the number of oscillations
     * @param springiness Defines the amplitude of the oscillations
     */
    constructor(
    /** Defines the number of oscillations*/
    oscillations?: number, 
    /** Defines the amplitude of the oscillations*/
    springiness?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with an exponential shape (see link below).
 * @see https://easings.net/#easeInExpo
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class ExponentialEase extends EasingFunction implements IEasingFunction {
    /** Defines the exponent of the function */
    exponent: number;
    /**
     * Instantiates an exponential easing function
     * @see https://easings.net/#easeInExpo
     * @param exponent Defines the exponent of the function
     */
    constructor(
    /** Defines the exponent of the function */
    exponent?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a power shape (see link below).
 * @see https://easings.net/#easeInQuad
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class PowerEase extends EasingFunction implements IEasingFunction {
    /** Defines the power of the function */
    power: number;
    /**
     * Instantiates an power base easing function
     * @see https://easings.net/#easeInQuad
     * @param power Defines the power of the function
     */
    constructor(
    /** Defines the power of the function */
    power?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a power of 2 shape (see link below).
 * @see https://easings.net/#easeInQuad
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class QuadraticEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a power of 4 shape (see link below).
 * @see https://easings.net/#easeInQuart
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class QuarticEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a power of 5 shape (see link below).
 * @see https://easings.net/#easeInQuint
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class QuinticEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a sin shape (see link below).
 * @see https://easings.net/#easeInSine
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class SineEase extends EasingFunction implements IEasingFunction {
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
/**
 * Easing function with a bezier shape (see link below).
 * @see http://cubic-bezier.com/#.17,.67,.83,.67
 * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#easing-functions
 */
export declare class BezierCurveEase extends EasingFunction implements IEasingFunction {
    /** Defines the x component of the start tangent in the bezier curve */
    x1: number;
    /** Defines the y component of the start tangent in the bezier curve */
    y1: number;
    /** Defines the x component of the end tangent in the bezier curve */
    x2: number;
    /** Defines the y component of the end tangent in the bezier curve */
    y2: number;
    /**
     * Instantiates a bezier function
     * @see http://cubic-bezier.com/#.17,.67,.83,.67
     * @param x1 Defines the x component of the start tangent in the bezier curve
     * @param y1 Defines the y component of the start tangent in the bezier curve
     * @param x2 Defines the x component of the end tangent in the bezier curve
     * @param y2 Defines the y component of the end tangent in the bezier curve
     */
    constructor(
    /** Defines the x component of the start tangent in the bezier curve */
    x1?: number, 
    /** Defines the y component of the start tangent in the bezier curve */
    y1?: number, 
    /** Defines the x component of the end tangent in the bezier curve */
    x2?: number, 
    /** Defines the y component of the end tangent in the bezier curve */
    y2?: number);
    /**
     * @internal
     */
    easeInCore(gradient: number): number;
}
