{"manifestVersion": "1.0.0", "name": "AgentAssistant", "displayName": "Multi-Agent Collaboration Plugin", "version": "2.0.0", "description": "Allows a primary AI or user to invoke specialized, pre-configured agents for various tasks. Supports both immediate and scheduled (time-delayed) communication. Each agent can have its own distinct system prompt and configuration.", "author": "VCP Team", "pluginType": "synchronous", "entryPoint": {"type": "nodejs", "command": "node AgentAssistant.js"}, "communication": {"protocol": "stdio", "timeout": 120000}, "configSchema": {"AGENT_ASSISTANT_MAX_HISTORY_ROUNDS": "integer", "AGENT_ASSISTANT_CONTEXT_TTL_HOURS": "integer"}, "capabilities": {"invocationCommands": [{"commandIdentifier": "InvokeAgent", "description": "[AgentAssistant Tool] Use this tool to consult or request assistance from a configured agent. Available agents: Nova (指挥中心), 活动推荐代理, 货币转换代理, 航班预订代理, 酒店预订代理. In your response, use the following precise format, ensuring all parameter values are enclosed in '「始」' and '「末」':\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」(Required) The exact display name of the agent to invoke (e.g., Nova, 活动推荐代理, 货币转换代理, 航班预订代理, 酒店预订代理). Refer to the agent list in your system configuration.「末」,\nprompt:「始」(Required) The question, command, or task you want the agent to handle. **Important: Please provide a brief self-introduction at the beginning of your prompt (e.g., 'I am [Your Identity/Name], and I would like you to...'),** so the agent can better understand the context of the request.「末」,\ntimely_contact:「始」(Optional) Set a future time to send this communication, in the format YYYY-MM-DD-HH:mm (e.g., 2025-12-31-18:00). If this field is provided, the communication will be scheduled for the specified time.「末」\n<<<[END_TOOL_REQUEST]>>>\n\nAgent Specializations:\n- Nova: 指挥中心，负责分派任务给其他agent\n- 活动推荐代理: 专业的旅游活动推荐，提供个性化行程规划\n- 货币转换代理: 专业的货币转换和金融服务，处理旅行中的货币问题\n- 航班预订代理: 专业的航班预订，提供航班搜索、比较和预订服务\n- 酒店预订代理: 专业的酒店预订，提供酒店搜索、比较和预订服务\n\nPlease select the most appropriate agent for your needs and clearly state your request (including a self-introduction).", "example": "```text\n// Immediate Communication Example\nI am the main AI, and the user needs travel planning assistance.\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」活动推荐代理「末」,\nprompt:「始」Hello 活动推荐代理, I am the main AI. The user wants to plan a 3-day trip to Tokyo and needs recommendations for activities and attractions. Please provide a detailed itinerary.「末」\n<<<[END_TOOL_REQUEST]>>>\n\n// Scheduled Communication Example\nI am the project manager. I need to remind about currency exchange tomorrow.\n\n<<<[TOOL_REQUEST]>>>\ntool_name:「始」AgentAssistant「末」,\nagent_name:「始」货币转换代理「末」,\nprompt:「始」Hello 货币转换代理, this is the project manager. Please remember to check the USD to JPY exchange rate tomorrow morning and provide an update.「末」,\ntimely_contact:「始」2025-08-21-09:00「末」\n<<<[END_TOOL_REQUEST]>>>\n```"}]}}