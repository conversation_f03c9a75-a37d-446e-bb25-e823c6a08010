{"version": 3, "file": "animationKey.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animationKey.ts"], "names": [], "mappings": "AA8BA;;GAEG;AACH,MAAM,CAAN,IAAY,yBASX;AATD,WAAY,yBAAyB;IACjC;;OAEG;IACH,yEAAQ,CAAA;IACR;;OAEG;IACH,yEAAQ,CAAA;AACZ,CAAC,EATW,yBAAyB,KAAzB,yBAAyB,QASpC", "sourcesContent": ["/**\r\n * Defines an interface which represents an animation key frame\r\n */\r\nexport interface IAnimationKey {\r\n    /**\r\n     * Frame of the key frame\r\n     */\r\n    frame: number;\r\n    /**\r\n     * Value at the specifies key frame\r\n     */\r\n    value: any;\r\n    /**\r\n     * The input tangent for the cubic hermite spline\r\n     */\r\n    inTangent?: any;\r\n    /**\r\n     * The output tangent for the cubic hermite spline\r\n     */\r\n    outTangent?: any;\r\n    /**\r\n     * The animation interpolation type\r\n     */\r\n    interpolation?: AnimationKeyInterpolation;\r\n    /**\r\n     * Property defined by UI tools to link (or not ) the tangents\r\n     */\r\n    lockedTangent?: boolean;\r\n}\r\n\r\n/**\r\n * Enum for the animation key frame interpolation type\r\n */\r\nexport enum AnimationKeyInterpolation {\r\n    /**\r\n     * Use tangents to interpolate between start and end values.\r\n     */\r\n    NONE = 0,\r\n    /**\r\n     * Do not interpolate between keys and use the start key value only. Tangents are ignored\r\n     */\r\n    STEP = 1,\r\n}\r\n"]}