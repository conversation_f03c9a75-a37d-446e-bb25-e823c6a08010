<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>协同 Canvas</title>
    <link rel="stylesheet" href="../styles/themes.css">
    <link rel="stylesheet" href="../style.css">
    <link rel="stylesheet" href="canvas.css"> <!-- Link to the new stylesheet -->
    <!-- CodeMirror 5 CSS -->
    <link rel="stylesheet" href="../node_modules/codemirror/lib/codemirror.css">
    <link rel="stylesheet" href="../node_modules/codemirror/theme/material-darker.css">
</head>
<body>
    <div id="custom-title-bar">
        <div class="title-text-container">
            <span class="title">VCP - Canvas</span>
        </div>
        <div class="window-controls">
            <button id="minimize-btn" class="window-control-btn" title="最小化">
                <svg width="10" height="10" viewBox="0 0 10 10"><line x1="0" y1="5" x2="10" y2="5" stroke="currentColor" stroke-width="1.2"></line></svg>
            </button>
            <button id="maximize-btn" class="window-control-btn" title="最大化">
                <svg width="10" height="10" viewBox="0 0 10 10"><path fill="none" stroke="currentColor" stroke-width="1" d="M0.5,0.5 h9 v9 h-9 z"></path></svg>
            </button>
            <button id="close-btn" class="window-control-btn" title="关闭">
                <svg width="10" height="10" viewBox="0 0 10 10"><path fill="currentColor" d="M0.5,0.5 9.5,9.5 M9.5,0.5 0.5,9.5" stroke="currentColor" stroke-width="1.2"></path></svg>
            </button>
        </div>
    </div>
    <div class="main-container">
        <aside class="sidebar glass-panel">
            <h3>Canvas目录</h3>
            <ul id="historyList">
                <!-- 历史记录将在这里动态加载 -->
            </ul>
            <button id="newCanvasBtn" class="global-action-button">新建 Canvas</button>
        </aside>
        <div class="resizer" id="resizer"></div>
        <div class="editor-container glass-panel">
            <div class="editor-top-bar">
                <button id="run-py-btn" class="action-btn">运行</button>
                <button id="render-md-btn" class="action-btn">渲染</button>
                <button id="render-html-btn" class="action-btn">渲染</button>
                <button id="toggle-wrap-btn" class="action-btn" style="display: block;">自动换行: 关</button>
            </div>
            <div class="editor-wrapper">
                <textarea id="editor"></textarea> <!-- CodeMirror 5 will replace this -->
            </div>
            <div class="status-bar">
                <span id="filePath">未保存</span>
                <span id="errorInfo">无错误</span>
            </div>
        </div>
        <div class="resizer" id="resizer-right"></div>
        <aside class="sidebar glass-panel" id="change-history-sidebar">
            <h3>文档变动</h3>
            <ul id="changeHistoryList">
                <!-- 变动历史将在这里动态加载 -->
            </ul>
        </aside>
    </div>

    <div id="context-menu" class="custom-context-menu">
        <button id="rename-btn">重命名</button>
        <button id="copy-btn">复制文件</button>
        <button id="delete-btn">删除文件</button>
    </div>

    <div id="editor-context-menu" class="custom-context-menu">
        <button data-action="undo">撤销</button>
        <button data-action="redo">重做</button>
        <hr>
        <button data-action="cut">剪切</button>
        <button data-action="copy">复制</button>
        <button data-action="paste">粘贴</button>
        <hr>
        <button data-action="selectAll">全选</button>
    </div>

    <!-- CodeMirror 5 JS -->
    <script src="../node_modules/codemirror/lib/codemirror.js"></script>
    <script src="../node_modules/codemirror/mode/javascript/javascript.js"></script>
    <script src="../node_modules/codemirror/mode/python/python.js"></script>
    <script src="../node_modules/codemirror/mode/css/css.js"></script>
    <script src="../node_modules/codemirror/mode/xml/xml.js"></script>
    <script src="../node_modules/codemirror/mode/htmlmixed/htmlmixed.js"></script>
    <script src="../node_modules/codemirror/mode/markdown/markdown.js"></script>
    <script src="../node_modules/codemirror/addon/comment/continuecomment.js"></script>
    <script src="canvas.js"></script>
</body>
</html>