{"version": 3, "file": "IAudioEngine.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Audio/Interfaces/IAudioEngine.ts"], "names": [], "mappings": "", "sourcesContent": ["import type { Observable } from \"../../Misc/observable\";\r\nimport type { IDisposable } from \"../../scene\";\r\nimport type { Nullable } from \"../../types\";\r\nimport type { Analyser } from \"../analyser\";\r\n\r\n/**\r\n * This represents an audio engine and it is responsible\r\n * to play, synchronize and analyse sounds throughout the application.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n */\r\nexport interface IAudioEngine extends IDisposable {\r\n    /**\r\n     * Gets whether the current host supports Web Audio and thus could create AudioContexts.\r\n     */\r\n    readonly canUseWebAudio: boolean;\r\n\r\n    /**\r\n     * Gets the current AudioContext if available.\r\n     */\r\n    readonly audioContext: Nullable<AudioContext>;\r\n\r\n    /**\r\n     * The master gain node defines the global audio volume of your audio engine.\r\n     */\r\n    readonly masterGain: GainNode;\r\n\r\n    /**\r\n     * Gets whether or not mp3 are supported by your browser.\r\n     */\r\n    readonly isMP3supported: boolean;\r\n\r\n    /**\r\n     * Gets whether or not ogg are supported by your browser.\r\n     */\r\n    readonly isOGGsupported: boolean;\r\n\r\n    /**\r\n     * Defines if Babylon should emit a warning if WebAudio is not supported.\r\n     * @ignoreNaming\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    WarnedWebAudioUnsupported: boolean;\r\n\r\n    /**\r\n     * Defines if the audio engine relies on a custom unlocked button.\r\n     * In this case, the embedded button will not be displayed.\r\n     */\r\n    useCustomUnlockedButton: boolean;\r\n\r\n    /**\r\n     * Gets whether or not the audio engine is unlocked (require first a user gesture on some browser).\r\n     */\r\n    readonly unlocked: boolean;\r\n\r\n    /**\r\n     * Event raised when audio has been unlocked on the browser.\r\n     */\r\n    onAudioUnlockedObservable: Observable<IAudioEngine>;\r\n\r\n    /**\r\n     * Event raised when audio has been locked on the browser.\r\n     */\r\n    onAudioLockedObservable: Observable<IAudioEngine>;\r\n\r\n    /**\r\n     * Flags the audio engine in Locked state.\r\n     * This happens due to new browser policies preventing audio to autoplay.\r\n     */\r\n    lock(): void;\r\n\r\n    /**\r\n     * Unlocks the audio engine once a user action has been done on the dom.\r\n     * This is helpful to resume play once browser policies have been satisfied.\r\n     */\r\n    unlock(): void;\r\n\r\n    /**\r\n     * Gets the global volume sets on the master gain.\r\n     * @returns the global volume if set or -1 otherwise\r\n     */\r\n    getGlobalVolume(): number;\r\n\r\n    /**\r\n     * Sets the global volume of your experience (sets on the master gain).\r\n     * @param newVolume Defines the new global volume of the application\r\n     */\r\n    setGlobalVolume(newVolume: number): void;\r\n\r\n    /**\r\n     * Connect the audio engine to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    connectToAnalyser(analyser: Analyser): void;\r\n}\r\n"]}