{"version": 3, "file": "pathCursor.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/pathCursor.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAG/C;;GAEG;AACH,MAAM,OAAO,UAAU;IAgBnB;;;OAGG;IACH,YAAoB,KAAY;QAAZ,UAAK,GAAL,KAAK,CAAO;QAnBhC;;WAEG;QACK,cAAS,GAAG,IAAI,KAAK,EAAgC,CAAC;QAE9D;;WAEG;QACH,UAAK,GAAW,CAAC,CAAC;QAElB;;WAEG;QACH,eAAU,GAAG,IAAI,KAAK,EAAa,CAAC;IAMD,CAAC;IAEpC;;;OAGG;IACI,QAAQ;QACX,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC9D,OAAO,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,OAAe,KAAK;QACjC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAEhB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,OAAe,KAAK;QAChC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;QAEjB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,IAAY;QACpB,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;YACpB,MAAM,kCAAkC,CAAC;SAC5C;QAED,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC;QACnB,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACK,aAAa;QACjB,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YACnB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;SACnB;QACD,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE;YACnB,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;SACnB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACK,cAAc;QAClB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;QAEvC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,CAA+B;QAC3C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ", "sourcesContent": ["import { Vector3 } from \"../Maths/math.vector\";\r\nimport type { Path2 } from \"../Maths/math.path\";\r\n\r\n/**\r\n * A cursor which tracks a point on a path\r\n */\r\nexport class PathCursor {\r\n    /**\r\n     * Stores path cursor callbacks for when an onchange event is triggered\r\n     */\r\n    private _onchange = new Array<(cursor: PathCursor) => void>();\r\n\r\n    /**\r\n     * The value of the path cursor\r\n     */\r\n    value: number = 0;\r\n\r\n    /**\r\n     * The animation array of the path cursor\r\n     */\r\n    animations = new Array<Animation>();\r\n\r\n    /**\r\n     * Initializes the path cursor\r\n     * @param _path The path to track\r\n     */\r\n    constructor(private _path: Path2) {}\r\n\r\n    /**\r\n     * Gets the cursor point on the path\r\n     * @returns A point on the path cursor at the cursor location\r\n     */\r\n    public getPoint(): Vector3 {\r\n        const point = this._path.getPointAtLengthPosition(this.value);\r\n        return new Vector3(point.x, 0, point.y);\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor ahead by the step amount\r\n     * @param step The amount to move the cursor forward\r\n     * @returns This path cursor\r\n     */\r\n    public moveAhead(step: number = 0.002): PathCursor {\r\n        this.move(step);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor behind by the step amount\r\n     * @param step The amount to move the cursor back\r\n     * @returns This path cursor\r\n     */\r\n    public moveBack(step: number = 0.002): PathCursor {\r\n        this.move(-step);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Moves the cursor by the step amount\r\n     * If the step amount is greater than one, an exception is thrown\r\n     * @param step The amount to move the cursor\r\n     * @returns This path cursor\r\n     */\r\n    public move(step: number): PathCursor {\r\n        if (Math.abs(step) > 1) {\r\n            throw \"step size should be less than 1.\";\r\n        }\r\n\r\n        this.value += step;\r\n        this._ensureLimits();\r\n        this._raiseOnChange();\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Ensures that the value is limited between zero and one\r\n     * @returns This path cursor\r\n     */\r\n    private _ensureLimits(): PathCursor {\r\n        while (this.value > 1) {\r\n            this.value -= 1;\r\n        }\r\n        while (this.value < 0) {\r\n            this.value += 1;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Runs onchange callbacks on change (used by the animation engine)\r\n     * @returns This path cursor\r\n     */\r\n    private _raiseOnChange(): PathCursor {\r\n        this._onchange.forEach((f) => f(this));\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Executes a function on change\r\n     * @param f A path cursor onchange callback\r\n     * @returns This path cursor\r\n     */\r\n    public onchange(f: (cursor: PathCursor) => void): PathCursor {\r\n        this._onchange.push(f);\r\n\r\n        return this;\r\n    }\r\n}\r\n"]}