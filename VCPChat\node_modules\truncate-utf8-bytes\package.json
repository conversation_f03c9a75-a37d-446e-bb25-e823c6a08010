{"name": "truncate-utf8-bytes", "version": "1.0.2", "description": "Truncate string to given length in bytes", "main": "index.js", "browser": "browser.js", "scripts": {"test": "tape test.js"}, "repository": {"type": "git", "url": "git+https://github.com/parshap/truncate-utf8-bytes.git"}, "keywords": ["truncate", "utf8"], "author": "<PERSON> <<EMAIL>>", "license": "WTFPL", "bugs": {"url": "https://github.com/parshap/truncate-utf8-bytes/issues"}, "homepage": "https://github.com/parshap/truncate-utf8-bytes#readme", "devDependencies": {"tape": "^4.2.2"}, "dependencies": {"utf8-byte-length": "^1.0.1"}}