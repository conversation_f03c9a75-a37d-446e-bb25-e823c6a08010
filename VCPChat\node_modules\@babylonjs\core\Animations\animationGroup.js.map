{"version": 3, "file": "animationGroup.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animationGroup.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAIxC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAEhD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAGrD,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AAEpC;;GAEG;AACH,MAAM,OAAO,iBAAiB;IAU1B;;;OAGG;IACI,YAAY;QACf,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QACpC,mBAAmB,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QAC3D,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;QAE9C,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ;AAED;;GAEG;AACH,MAAM,OAAO,cAAc;IAwDvB;;OAEG;IACH,IAAW,IAAI;QACX,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,EAAE;QACT,OAAO,IAAI,CAAC,GAAG,CAAC;IACpB,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC;IAC9C,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU,CAAC,KAAa;QAC/B,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B,CAAC;IAED,IAAW,aAAa,CAAC,KAAc;QACnC,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;YAC/B,OAAO;SACV;QAED,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC;SAClD;IACL,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED,IAAW,UAAU,CAAC,KAAc;QAChC,IAAI,IAAI,CAAC,WAAW,KAAK,KAAK,EAAE;YAC5B,OAAO;SACV;QAED,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAEzB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;SAC5C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,kBAAkB;QACzB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,IAAW,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,IAAW,QAAQ;QACf,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC,CAAC;IAED;;;;;;OAMG;IACH;IACI,sCAAsC;IAC/B,IAAY,EACnB,QAAyB,IAAI;QADtB,SAAI,GAAJ,IAAI,CAAQ;QA9Kf,wBAAmB,GAAG,IAAI,KAAK,EAAqB,CAAC;QACrD,iBAAY,GAAG,IAAI,KAAK,EAAc,CAAC;QACvC,UAAK,GAAG,MAAM,CAAC,SAAS,CAAC;QACzB,QAAG,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC;QAGxB,gBAAW,GAAG,CAAC,CAAC;QAChB,mBAAc,GAAG,KAAK,CAAC;QACvB,gBAAW,GAAG,KAAK,CAAC;QAE5B,gBAAgB;QACT,qBAAgB,GAA4B,IAAI,CAAC;QAOxD;;WAEG;QACI,6BAAwB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAEtE;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAqB,CAAC;QAEvE;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAEzE;;WAEG;QACI,kCAA6B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAExE;;WAEG;QACI,oCAA+B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAE1E;;WAEG;QACI,mCAA8B,GAAG,IAAI,UAAU,EAAkB,CAAC;QAEzE;;WAEG;QACI,aAAQ,GAAQ,IAAI,CAAC;QAgNpB,wBAAmB,GAAc,EAAE,CAAC;QAlFxC,IAAI,CAAC,MAAM,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAiB,CAAC;QACrD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;QAE1C,IAAI,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACI,oBAAoB,CAAC,SAAoB,EAAE,MAAW;QACzD,MAAM,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;QAClD,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;QACxC,iBAAiB,CAAC,MAAM,GAAG,MAAM,CAAC;QAElC,MAAM,IAAI,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;YAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;SAC9B;QAED,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;YACxC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;SAC1C;QAED,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QAEjD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAED;;;;;;OAMG;IACI,SAAS,CAAC,aAA+B,IAAI,EAAE,WAA6B,IAAI;QACnF,IAAI,UAAU,IAAI,IAAI,EAAE;YACpB,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;SAC3B;QACD,IAAI,QAAQ,IAAI,IAAI,EAAE;YAClB,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;SACvB;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,IAAI,GAAG,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;YACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACzB,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErC,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,EAAE;gBAC7B,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,UAAU;oBACjB,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,SAAS,EAAE,QAAQ,CAAC,SAAS;oBAC7B,UAAU,EAAE,QAAQ,CAAC,UAAU;oBAC/B,aAAa,EAAE,QAAQ,CAAC,aAAa;iBACxC,CAAC;gBACF,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aAC7B;YAED,IAAI,MAAM,CAAC,KAAK,GAAG,QAAQ,EAAE;gBACzB,MAAM,MAAM,GAAkB;oBAC1B,KAAK,EAAE,QAAQ;oBACf,KAAK,EAAE,MAAM,CAAC,KAAK;oBACnB,SAAS,EAAE,MAAM,CAAC,SAAS;oBAC3B,UAAU,EAAE,MAAM,CAAC,UAAU;oBAC7B,aAAa,EAAE,MAAM,CAAC,aAAa;iBACtC,CAAC;gBACF,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACrB;SACJ;QAED,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QACxB,IAAI,CAAC,GAAG,GAAG,QAAQ,CAAC;QAEpB,OAAO,IAAI,CAAC;IAChB,CAAC;IAKO,YAAY,CAAC,UAAsB,EAAE,iBAAoC,EAAE,KAAa;QAC5F,UAAU,CAAC,eAAe,GAAG,GAAG,EAAE;YAC9B,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;YAElE,IAAI,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,EAAE;gBACjC,OAAO;aACV;YAED,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;YAEvC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,IAAI,IAAI,CAAC,mBAAmB,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;gBAC9D,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;gBAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;aACvC;QACL,CAAC,CAAC;IACN,CAAC;IAED;;;;;;;;OAQG;IACI,KAAK,CAAC,IAAI,GAAG,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,IAAa,EAAE,EAAW,EAAE,UAAoB;QACvF,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1D,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QAE3B,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;QAC7B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QAEpC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAClE,MAAM,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAC/C,iBAAiB,CAAC,MAAM,EACxB,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAC7B,IAAI,KAAK,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EACtC,EAAE,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAChC,IAAI,EACJ,UAAU,EACV,SAAS,EACT,SAAS,EACT,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAC3D,CAAC;YACF,UAAU,CAAC,cAAc,GAAG,GAAG,EAAE;gBAC7B,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;gBACjE,IAAI,CAAC,yBAAyB,CAAC,UAAU,CAAC,CAAC;YAC/C,CAAC,CAAC;YAEF,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SACtC;QAED,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QACvB,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QAEtB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;SACtB;QAED,IAAI,CAAC,+BAA+B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE3D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,IAAc;QACtB,8CAA8C;QAC9C,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE;YAChF,IAAI,IAAI,KAAK,SAAS,EAAE;gBACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;aAC7B;YACD,IAAI,CAAC,OAAO,EAAE,CAAC;SAClB;aAAM;YACH,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;SACtC;QAED,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;QAEvB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAClB,IAAI,CAAC,IAAI,EAAE,CAAC;YACZ,OAAO,IAAI,CAAC;SACf;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,EAAE,CAAC;SACtB;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,OAAO,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAE1D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;OAGG;IACI,IAAI;QACP,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QACvC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9C,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;SAChD;QAED,wDAAwD;QACxD,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACxE,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACzD,IAAI,UAAU,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1C,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC,GAAG,UAAU,CAAC;aAC3D;SACJ;QACD,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,GAAG,QAAQ,CAAC;QAEjD,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,0BAA0B,CAAC,MAAc;QAC5C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;SAC9B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACI,qBAAqB,CAAC,IAA0B;QACnD,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SAC7B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,KAAa;QAC1B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,IAAI,CAAC;SACf;QAED,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC3D,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;YAC5C,UAAU,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;SAC/B;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC;QAE7B,oBAAoB;QACpB,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExD,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SAChD;QAED,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAClE,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACZ,IAAI,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;aAC1D;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;QAED,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,CAAC;QACtC,IAAI,CAAC,6BAA6B,CAAC,KAAK,EAAE,CAAC;QAC3C,IAAI,CAAC,+BAA+B,CAAC,KAAK,EAAE,CAAC;QAC7C,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;QAC5C,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,CAAC;QACvC,IAAI,CAAC,8BAA8B,CAAC,KAAK,EAAE,CAAC;IAChD,CAAC;IAEO,yBAAyB,CAAC,UAAsB;QACpD,8CAA8C;QAC9C,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,IAAI,GAAG,GAAG,CAAC,CAAC,EAAE;YACV,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;SACpC;QAED,uDAAuD;QACvD,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;YAChC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,IAAI,CAAC,6BAA6B,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC5D;IACL,CAAC;IAED;;;;;;OAMG;IACI,KAAK,CAAC,OAAe,EAAE,eAAyC,EAAE,eAAe,GAAG,KAAK;QAC5F,MAAM,QAAQ,GAAG,IAAI,cAAc,CAAC,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAEvE,KAAK,MAAM,eAAe,IAAI,IAAI,CAAC,mBAAmB,EAAE;YACpD,QAAQ,CAAC,oBAAoB,CACzB,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,eAAe,CAAC,SAAS,EAC/E,eAAe,CAAC,CAAC,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CACrF,CAAC;SACL;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;QACjC,mBAAmB,CAAC,kBAAkB,GAAG,EAAE,CAAC;QAC5C,KAAK,IAAI,sBAAsB,GAAG,CAAC,EAAE,sBAAsB,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,sBAAsB,EAAE,EAAE;YACpH,MAAM,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;YAC1E,mBAAmB,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,GAAG,iBAAiB,CAAC,SAAS,EAAE,CAAC;SAClG;QAED,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YAC5B,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;SACjD;QAED,WAAW;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;SAChD;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAED,UAAU;IACV;;;;;OAKG;IACI,MAAM,CAAC,KAAK,CAAC,oBAAyB,EAAE,KAAY;QACvD,MAAM,cAAc,GAAG,IAAI,cAAc,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAC5E,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrE,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACrE,MAAM,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAC/D,MAAM,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC;YACtC,IAAI,iBAAiB,CAAC,SAAS,CAAC,QAAQ,KAAK,WAAW,EAAE;gBACtD,yBAAyB;gBACzB,MAAM,WAAW,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;gBACjD,IAAI,WAAW,EAAE;oBACb,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;iBAC/D;aACJ;iBAAM;gBACH,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;gBAEzC,IAAI,UAAU,IAAI,IAAI,EAAE;oBACpB,cAAc,CAAC,oBAAoB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;iBAC9D;aACJ;SACJ;QAED,IAAI,oBAAoB,CAAC,IAAI,KAAK,IAAI,IAAI,oBAAoB,CAAC,EAAE,KAAK,IAAI,EAAE;YACxE,cAAc,CAAC,SAAS,CAAC,oBAAoB,CAAC,IAAI,EAAE,oBAAoB,CAAC,EAAE,CAAC,CAAC;SAChF;QAED,IAAI,IAAI,EAAE;YACN,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;SAC7D;QAED,IAAI,oBAAoB,CAAC,QAAQ,KAAK,SAAS,EAAE;YAC7C,cAAc,CAAC,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC;SAC3D;QAED,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,qBAAqB,CAAC,oBAAoC,EAAE,cAAc,GAAG,CAAC,EAAE,KAAc,EAAE,aAAa,GAAG,KAAK,EAAE,UAAmB;QACpJ,IAAI,cAAc,GAAG,oBAAoB,CAAC;QAC1C,IAAI,aAAa,EAAE;YACf,cAAc,GAAG,oBAAoB,CAAC,KAAK,CAAC,UAAU,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;SAClF;QAED,MAAM,kBAAkB,GAAG,cAAc,CAAC,kBAAkB,CAAC;QAC7D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC5D,MAAM,iBAAiB,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;YACpD,SAAS,CAAC,qBAAqB,CAAC,iBAAiB,CAAC,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;SACvF;QAED,cAAc,CAAC,UAAU,GAAG,IAAI,CAAC;QAEjC,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;OAGG;IACI,YAAY;QACf,OAAO,gBAAgB,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QAC/B,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACxC,IAAI,WAAW,EAAE;YACb,GAAG,IAAI,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC;YAC/B,GAAG,IAAI,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC;YAC3B,GAAG,IAAI,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC;YACzC,GAAG,IAAI,gBAAgB,GAAG,IAAI,CAAC,WAAW,CAAC;YAC3C,GAAG,IAAI,+BAA+B,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC;YACzE,GAAG,IAAI,wBAAwB,GAAG,IAAI,CAAC,YAAY,CAAC;SACvD;QACD,OAAO,GAAG,CAAC;IACf,CAAC;CACJ", "sourcesContent": ["import type { Animatable } from \"./animatable\";\r\nimport { Animation } from \"./animation\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\n\r\nimport type { Scene, IDisposable } from \"../scene\";\r\nimport { Observable } from \"../Misc/observable\";\r\nimport type { Nullable } from \"../types\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\nimport type { AbstractScene } from \"../abstractScene\";\r\nimport { Tags } from \"../Misc/tags\";\r\n\r\n/**\r\n * This class defines the direct association between an animation and a target\r\n */\r\nexport class TargetedAnimation {\r\n    /**\r\n     * Animation to perform\r\n     */\r\n    public animation: Animation;\r\n    /**\r\n     * Target to animate\r\n     */\r\n    public target: any;\r\n\r\n    /**\r\n     * Returns the string \"TargetedAnimation\"\r\n     * @returns \"TargetedAnimation\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"TargetedAnimation\";\r\n    }\r\n\r\n    /**\r\n     * Serialize the object\r\n     * @returns the JSON object representing the current entity\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n        serializationObject.animation = this.animation.serialize();\r\n        serializationObject.targetId = this.target.id;\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n\r\n/**\r\n * Use this class to create coordinated animations on multiple targets\r\n */\r\nexport class AnimationGroup implements IDisposable {\r\n    private _scene: Scene;\r\n\r\n    private _targetedAnimations = new Array<TargetedAnimation>();\r\n    private _animatables = new Array<Animatable>();\r\n    private _from = Number.MAX_VALUE;\r\n    private _to = -Number.MAX_VALUE;\r\n    private _isStarted: boolean;\r\n    private _isPaused: boolean;\r\n    private _speedRatio = 1;\r\n    private _loopAnimation = false;\r\n    private _isAdditive = false;\r\n\r\n    /** @internal */\r\n    public _parentContainer: Nullable<AbstractScene> = null;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the node\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /**\r\n     * This observable will notify when one animation have ended\r\n     */\r\n    public onAnimationEndObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when one animation loops\r\n     */\r\n    public onAnimationLoopObservable = new Observable<TargetedAnimation>();\r\n\r\n    /**\r\n     * Observer raised when all animations have looped\r\n     */\r\n    public onAnimationGroupLoopObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have ended.\r\n     */\r\n    public onAnimationGroupEndObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations have paused.\r\n     */\r\n    public onAnimationGroupPauseObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * This observable will notify when all animations are playing.\r\n     */\r\n    public onAnimationGroupPlayObservable = new Observable<AnimationGroup>();\r\n\r\n    /**\r\n     * Gets or sets an object used to store user defined information for the node\r\n     */\r\n    public metadata: any = null;\r\n\r\n    /**\r\n     * Gets the first frame\r\n     */\r\n    public get from(): number {\r\n        return this._from;\r\n    }\r\n\r\n    /**\r\n     * Gets the last frame\r\n     */\r\n    public get to(): number {\r\n        return this._to;\r\n    }\r\n\r\n    /**\r\n     * Define if the animations are started\r\n     */\r\n    public get isStarted(): boolean {\r\n        return this._isStarted;\r\n    }\r\n\r\n    /**\r\n     * Gets a value indicating that the current group is playing\r\n     */\r\n    public get isPlaying(): boolean {\r\n        return this._isStarted && !this._isPaused;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public get speedRatio(): number {\r\n        return this._speedRatio;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the speed ratio to use for all animations\r\n     */\r\n    public set speedRatio(value: number) {\r\n        if (this._speedRatio === value) {\r\n            return;\r\n        }\r\n\r\n        this._speedRatio = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.speedRatio = this._speedRatio;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should loop or not\r\n     */\r\n    public get loopAnimation(): boolean {\r\n        return this._loopAnimation;\r\n    }\r\n\r\n    public set loopAnimation(value: boolean) {\r\n        if (this._loopAnimation === value) {\r\n            return;\r\n        }\r\n\r\n        this._loopAnimation = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.loopAnimation = this._loopAnimation;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets or sets if all animations should be evaluated additively\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._isAdditive;\r\n    }\r\n\r\n    public set isAdditive(value: boolean) {\r\n        if (this._isAdditive === value) {\r\n            return;\r\n        }\r\n\r\n        this._isAdditive = value;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.isAdditive = this._isAdditive;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the targeted animations for this animation group\r\n     */\r\n    public get targetedAnimations(): Array<TargetedAnimation> {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * returning the list of animatables controlled by this animation group.\r\n     */\r\n    public get animatables(): Array<Animatable> {\r\n        return this._animatables;\r\n    }\r\n\r\n    /**\r\n     * Gets the list of target animations\r\n     */\r\n    public get children() {\r\n        return this._targetedAnimations;\r\n    }\r\n\r\n    /**\r\n     * Instantiates a new Animation Group.\r\n     * This helps managing several animations at once.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations\r\n     * @param name Defines the name of the group\r\n     * @param scene Defines the scene the group belongs to\r\n     */\r\n    public constructor(\r\n        /** The name of the animation group */\r\n        public name: string,\r\n        scene: Nullable<Scene> = null\r\n    ) {\r\n        this._scene = scene || EngineStore.LastCreatedScene!;\r\n        this.uniqueId = this._scene.getUniqueId();\r\n\r\n        this._scene.addAnimationGroup(this);\r\n    }\r\n\r\n    /**\r\n     * Add an animation (with its target) in the group\r\n     * @param animation defines the animation we want to add\r\n     * @param target defines the target of the animation\r\n     * @returns the TargetedAnimation object\r\n     */\r\n    public addTargetedAnimation(animation: Animation, target: any): TargetedAnimation {\r\n        const targetedAnimation = new TargetedAnimation();\r\n        targetedAnimation.animation = animation;\r\n        targetedAnimation.target = target;\r\n\r\n        const keys = animation.getKeys();\r\n        if (this._from > keys[0].frame) {\r\n            this._from = keys[0].frame;\r\n        }\r\n\r\n        if (this._to < keys[keys.length - 1].frame) {\r\n            this._to = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        this._targetedAnimations.push(targetedAnimation);\r\n\r\n        return targetedAnimation;\r\n    }\r\n\r\n    /**\r\n     * This function will normalize every animation in the group to make sure they all go from beginFrame to endFrame\r\n     * It can add constant keys at begin or end\r\n     * @param beginFrame defines the new begin frame for all animations or the smallest begin frame of all animations if null (defaults to null)\r\n     * @param endFrame defines the new end frame for all animations or the largest end frame of all animations if null (defaults to null)\r\n     * @returns the animation group\r\n     */\r\n    public normalize(beginFrame: Nullable<number> = null, endFrame: Nullable<number> = null): AnimationGroup {\r\n        if (beginFrame == null) {\r\n            beginFrame = this._from;\r\n        }\r\n        if (endFrame == null) {\r\n            endFrame = this._to;\r\n        }\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const keys = targetedAnimation.animation.getKeys();\r\n            const startKey = keys[0];\r\n            const endKey = keys[keys.length - 1];\r\n\r\n            if (startKey.frame > beginFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: beginFrame,\r\n                    value: startKey.value,\r\n                    inTangent: startKey.inTangent,\r\n                    outTangent: startKey.outTangent,\r\n                    interpolation: startKey.interpolation,\r\n                };\r\n                keys.splice(0, 0, newKey);\r\n            }\r\n\r\n            if (endKey.frame < endFrame) {\r\n                const newKey: IAnimationKey = {\r\n                    frame: endFrame,\r\n                    value: endKey.value,\r\n                    inTangent: endKey.inTangent,\r\n                    outTangent: endKey.outTangent,\r\n                    interpolation: endKey.interpolation,\r\n                };\r\n                keys.push(newKey);\r\n            }\r\n        }\r\n\r\n        this._from = beginFrame;\r\n        this._to = endFrame;\r\n\r\n        return this;\r\n    }\r\n\r\n    private _animationLoopCount: number;\r\n    private _animationLoopFlags: boolean[] = [];\r\n\r\n    private _processLoop(animatable: Animatable, targetedAnimation: TargetedAnimation, index: number) {\r\n        animatable.onAnimationLoop = () => {\r\n            this.onAnimationLoopObservable.notifyObservers(targetedAnimation);\r\n\r\n            if (this._animationLoopFlags[index]) {\r\n                return;\r\n            }\r\n\r\n            this._animationLoopFlags[index] = true;\r\n\r\n            this._animationLoopCount++;\r\n            if (this._animationLoopCount === this._targetedAnimations.length) {\r\n                this.onAnimationGroupLoopObservable.notifyObservers(this);\r\n                this._animationLoopCount = 0;\r\n                this._animationLoopFlags.length = 0;\r\n            }\r\n        };\r\n    }\r\n\r\n    /**\r\n     * Start all animations on given targets\r\n     * @param loop defines if animations must loop\r\n     * @param speedRatio defines the ratio to apply to animation speed (1 by default)\r\n     * @param from defines the from key (optional)\r\n     * @param to defines the to key (optional)\r\n     * @param isAdditive defines the additive state for the resulting animatables (optional)\r\n     * @returns the current animation group\r\n     */\r\n    public start(loop = false, speedRatio = 1, from?: number, to?: number, isAdditive?: boolean): AnimationGroup {\r\n        if (this._isStarted || this._targetedAnimations.length === 0) {\r\n            return this;\r\n        }\r\n\r\n        this._loopAnimation = loop;\r\n\r\n        this._animationLoopCount = 0;\r\n        this._animationLoopFlags.length = 0;\r\n\r\n        for (let index = 0; index < this._targetedAnimations.length; index++) {\r\n            const targetedAnimation = this._targetedAnimations[index];\r\n            const animatable = this._scene.beginDirectAnimation(\r\n                targetedAnimation.target,\r\n                [targetedAnimation.animation],\r\n                from !== undefined ? from : this._from,\r\n                to !== undefined ? to : this._to,\r\n                loop,\r\n                speedRatio,\r\n                undefined,\r\n                undefined,\r\n                isAdditive !== undefined ? isAdditive : this._isAdditive\r\n            );\r\n            animatable.onAnimationEnd = () => {\r\n                this.onAnimationEndObservable.notifyObservers(targetedAnimation);\r\n                this._checkAnimationGroupEnded(animatable);\r\n            };\r\n\r\n            this._processLoop(animatable, targetedAnimation, index);\r\n            this._animatables.push(animatable);\r\n        }\r\n\r\n        this._speedRatio = speedRatio;\r\n\r\n        this._isStarted = true;\r\n        this._isPaused = false;\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Pause all animations\r\n     * @returns the animation group\r\n     */\r\n    public pause(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        this._isPaused = true;\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.pause();\r\n        }\r\n\r\n        this.onAnimationGroupPauseObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Play all animations to initial state\r\n     * This function will start() the animations if they were not started or will restart() them if they were paused\r\n     * @param loop defines if animations must loop\r\n     * @returns the animation group\r\n     */\r\n    public play(loop?: boolean): AnimationGroup {\r\n        // only if all animatables are ready and exist\r\n        if (this.isStarted && this._animatables.length === this._targetedAnimations.length) {\r\n            if (loop !== undefined) {\r\n                this.loopAnimation = loop;\r\n            }\r\n            this.restart();\r\n        } else {\r\n            this.stop();\r\n            this.start(loop, this._speedRatio);\r\n        }\r\n\r\n        this._isPaused = false;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Reset all animations to initial state\r\n     * @returns the animation group\r\n     */\r\n    public reset(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            this.play();\r\n            this.goToFrame(0);\r\n            this.stop();\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.reset();\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Restart animations from key 0\r\n     * @returns the animation group\r\n     */\r\n    public restart(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.restart();\r\n        }\r\n\r\n        this.onAnimationGroupPlayObservable.notifyObservers(this);\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Stop all animations\r\n     * @returns the animation group\r\n     */\r\n    public stop(): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        const list = this._animatables.slice();\r\n        for (let index = 0; index < list.length; index++) {\r\n            list[index].stop(undefined, undefined, true);\r\n        }\r\n\r\n        // We will take care of removing all stopped animatables\r\n        let curIndex = 0;\r\n        for (let index = 0; index < this._scene._activeAnimatables.length; index++) {\r\n            const animatable = this._scene._activeAnimatables[index];\r\n            if (animatable._runtimeAnimations.length > 0) {\r\n                this._scene._activeAnimatables[curIndex++] = animatable;\r\n            }\r\n        }\r\n        this._scene._activeAnimatables.length = curIndex;\r\n\r\n        this._isStarted = false;\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Set animation weight for all animatables\r\n     * @param weight defines the weight to use\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public setWeightForAllAnimatables(weight: number): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.weight = weight;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Synchronize and normalize all animatables with a source animatable\r\n     * @param root defines the root animatable to synchronize with (null to stop synchronizing)\r\n     * @returns the animationGroup\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights\r\n     */\r\n    public syncAllAnimationsWith(root: Nullable<Animatable>): AnimationGroup {\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.syncWith(root);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Goes to a specific frame in this animation group\r\n     * @param frame the frame number to go to\r\n     * @returns the animationGroup\r\n     */\r\n    public goToFrame(frame: number): AnimationGroup {\r\n        if (!this._isStarted) {\r\n            return this;\r\n        }\r\n\r\n        for (let index = 0; index < this._animatables.length; index++) {\r\n            const animatable = this._animatables[index];\r\n            animatable.goToFrame(frame);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /**\r\n     * Dispose all associated resources\r\n     */\r\n    public dispose(): void {\r\n        this._targetedAnimations.length = 0;\r\n        this._animatables.length = 0;\r\n\r\n        // Remove from scene\r\n        const index = this._scene.animationGroups.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._scene.animationGroups.splice(index, 1);\r\n        }\r\n\r\n        if (this._parentContainer) {\r\n            const index = this._parentContainer.animationGroups.indexOf(this);\r\n            if (index > -1) {\r\n                this._parentContainer.animationGroups.splice(index, 1);\r\n            }\r\n            this._parentContainer = null;\r\n        }\r\n\r\n        this.onAnimationEndObservable.clear();\r\n        this.onAnimationGroupEndObservable.clear();\r\n        this.onAnimationGroupPauseObservable.clear();\r\n        this.onAnimationGroupPlayObservable.clear();\r\n        this.onAnimationLoopObservable.clear();\r\n        this.onAnimationGroupLoopObservable.clear();\r\n    }\r\n\r\n    private _checkAnimationGroupEnded(animatable: Animatable) {\r\n        // animatable should be taken out of the array\r\n        const idx = this._animatables.indexOf(animatable);\r\n        if (idx > -1) {\r\n            this._animatables.splice(idx, 1);\r\n        }\r\n\r\n        // all animatables were removed? animation group ended!\r\n        if (this._animatables.length === 0) {\r\n            this._isStarted = false;\r\n            this.onAnimationGroupEndObservable.notifyObservers(this);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Clone the current animation group and returns a copy\r\n     * @param newName defines the name of the new group\r\n     * @param targetConverter defines an optional function used to convert current animation targets to new ones\r\n     * @param cloneAnimations defines if the animations should be cloned or referenced\r\n     * @returns the new animation group\r\n     */\r\n    public clone(newName: string, targetConverter?: (oldTarget: any) => any, cloneAnimations = false): AnimationGroup {\r\n        const newGroup = new AnimationGroup(newName || this.name, this._scene);\r\n\r\n        for (const targetAnimation of this._targetedAnimations) {\r\n            newGroup.addTargetedAnimation(\r\n                cloneAnimations ? targetAnimation.animation.clone() : targetAnimation.animation,\r\n                targetConverter ? targetConverter(targetAnimation.target) : targetAnimation.target\r\n            );\r\n        }\r\n\r\n        return newGroup;\r\n    }\r\n\r\n    /**\r\n     * Serializes the animationGroup to an object\r\n     * @returns Serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.from = this.from;\r\n        serializationObject.to = this.to;\r\n        serializationObject.targetedAnimations = [];\r\n        for (let targetedAnimationIndex = 0; targetedAnimationIndex < this.targetedAnimations.length; targetedAnimationIndex++) {\r\n            const targetedAnimation = this.targetedAnimations[targetedAnimationIndex];\r\n            serializationObject.targetedAnimations[targetedAnimationIndex] = targetedAnimation.serialize();\r\n        }\r\n\r\n        if (Tags && Tags.HasTags(this)) {\r\n            serializationObject.tags = Tags.GetTags(this);\r\n        }\r\n\r\n        // Metadata\r\n        if (this.metadata) {\r\n            serializationObject.metadata = this.metadata;\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Returns a new AnimationGroup object parsed from the source provided.\r\n     * @param parsedAnimationGroup defines the source\r\n     * @param scene defines the scene that will receive the animationGroup\r\n     * @returns a new AnimationGroup\r\n     */\r\n    public static Parse(parsedAnimationGroup: any, scene: Scene): AnimationGroup {\r\n        const animationGroup = new AnimationGroup(parsedAnimationGroup.name, scene);\r\n        for (let i = 0; i < parsedAnimationGroup.targetedAnimations.length; i++) {\r\n            const targetedAnimation = parsedAnimationGroup.targetedAnimations[i];\r\n            const animation = Animation.Parse(targetedAnimation.animation);\r\n            const id = targetedAnimation.targetId;\r\n            if (targetedAnimation.animation.property === \"influence\") {\r\n                // morph target animation\r\n                const morphTarget = scene.getMorphTargetById(id);\r\n                if (morphTarget) {\r\n                    animationGroup.addTargetedAnimation(animation, morphTarget);\r\n                }\r\n            } else {\r\n                const targetNode = scene.getNodeById(id);\r\n\r\n                if (targetNode != null) {\r\n                    animationGroup.addTargetedAnimation(animation, targetNode);\r\n                }\r\n            }\r\n        }\r\n\r\n        if (parsedAnimationGroup.from !== null && parsedAnimationGroup.to !== null) {\r\n            animationGroup.normalize(parsedAnimationGroup.from, parsedAnimationGroup.to);\r\n        }\r\n\r\n        if (Tags) {\r\n            Tags.AddTagsTo(animationGroup, parsedAnimationGroup.tags);\r\n        }\r\n\r\n        if (parsedAnimationGroup.metadata !== undefined) {\r\n            animationGroup.metadata = parsedAnimationGroup.metadata;\r\n        }\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to\r\n     * @param range defines the name of the AnimationRange belonging to the animations in the group to convert\r\n     * @param cloneOriginal defines whether or not to clone the group and convert the clone or convert the original group (default is false)\r\n     * @param clonedName defines the name of the resulting cloned AnimationGroup if cloneOriginal is true\r\n     * @returns a new AnimationGroup if cloneOriginal is true or the original AnimationGroup if cloneOriginal is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, referenceFrame = 0, range?: string, cloneOriginal = false, clonedName?: string): AnimationGroup {\r\n        let animationGroup = sourceAnimationGroup;\r\n        if (cloneOriginal) {\r\n            animationGroup = sourceAnimationGroup.clone(clonedName || animationGroup.name);\r\n        }\r\n\r\n        const targetedAnimations = animationGroup.targetedAnimations;\r\n        for (let index = 0; index < targetedAnimations.length; index++) {\r\n            const targetedAnimation = targetedAnimations[index];\r\n            Animation.MakeAnimationAdditive(targetedAnimation.animation, referenceFrame, range);\r\n        }\r\n\r\n        animationGroup.isAdditive = true;\r\n\r\n        return animationGroup;\r\n    }\r\n\r\n    /**\r\n     * Returns the string \"AnimationGroup\"\r\n     * @returns \"AnimationGroup\"\r\n     */\r\n    public getClassName(): string {\r\n        return \"AnimationGroup\";\r\n    }\r\n\r\n    /**\r\n     * Creates a detailed string about the object\r\n     * @param fullDetails defines if the output string will support multiple levels of logging within scene loading\r\n     * @returns a string representing the object\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name;\r\n        ret += \", type: \" + this.getClassName();\r\n        if (fullDetails) {\r\n            ret += \", from: \" + this._from;\r\n            ret += \", to: \" + this._to;\r\n            ret += \", isStarted: \" + this._isStarted;\r\n            ret += \", speedRatio: \" + this._speedRatio;\r\n            ret += \", targetedAnimations length: \" + this._targetedAnimations.length;\r\n            ret += \", animatables length: \" + this._animatables;\r\n        }\r\n        return ret;\r\n    }\r\n}\r\n"]}