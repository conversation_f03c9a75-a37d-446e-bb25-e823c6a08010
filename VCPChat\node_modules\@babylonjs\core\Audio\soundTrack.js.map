{"version": 3, "file": "soundTrack.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Audio/soundTrack.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAC3C,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAgBrD;;;;GAIG;AACH,MAAM,OAAO,UAAU;IAgBnB;;;;;OAKG;IACH,YAAY,KAAuB,EAAE,UAA8B,EAAE;QArBrE;;WAEG;QACI,OAAE,GAAW,CAAC,CAAC,CAAC;QAUf,mBAAc,GAAG,KAAK,CAAC;QAS3B,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,IAAI,KAAK,EAAE,CAAC;QACnC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YACrD,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACnC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,+BAA+B;;QACnC,IAAI,CAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,KAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;YACvE,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;YACrE,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;YAE7D,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;oBACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;iBAC3D;aACJ;YAED,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,cAAc,EAAE;YACzD,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;aAC7C;YACD,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;gBAChC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;aACrC;YACD,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;aACtC;YACD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;SAChC;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,KAAY;;QACxB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,+BAA+B,EAAE,CAAC;SAC1C;QACD,IAAI,CAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,KAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7D,KAAK,CAAC,4BAA4B,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;SAC7D;QACD,IAAI,KAAK,CAAC,YAAY,EAAE;YACpB,IAAI,KAAK,CAAC,YAAY,KAAK,CAAC,CAAC,EAAE;gBAC3B,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aACjD;iBAAM,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;gBAChC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;aAClE;SACJ;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACjC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC;IACjC,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,KAAY;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAClD,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YACd,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACzC;IACL,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,SAAiB;;QAC9B,IAAI,CAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,KAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7D,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;SAChD;IACL,CAAC;IAED;;;;OAIG;IACI,wBAAwB;;QAC3B,IAAI,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,EAAE;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC;aACtD;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,8BAA8B;;QACjC,IAAI,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,EAAE;YACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAClD,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAC;aAC5D;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,iBAAiB,CAAC,QAAkB;;QACvC,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,IAAI,CAAC,kBAAkB,CAAC,eAAe,EAAE,CAAC;SAC7C;QACD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAI,CAAA,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,KAAI,IAAI,CAAC,gBAAgB,EAAE;YAC7D,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC;YACnC,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,EAAE,MAAM,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;SACnG;IACL,CAAC;CACJ", "sourcesContent": ["import type { Sound } from \"./sound\";\r\nimport type { Analyser } from \"./analyser\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\n/**\r\n * Options allowed during the creation of a sound track.\r\n */\r\nexport interface ISoundTrackOptions {\r\n    /**\r\n     * The volume the sound track should take during creation\r\n     */\r\n    volume?: number;\r\n    /**\r\n     * Define if the sound track is the main sound track of the scene\r\n     */\r\n    mainTrack?: boolean;\r\n}\r\n\r\n/**\r\n * It could be useful to isolate your music & sounds on several tracks to better manage volume on a grouped instance of sounds.\r\n * It will be also used in a future release to apply effects on a specific track.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\r\n */\r\nexport class SoundTrack {\r\n    /**\r\n     * The unique identifier of the sound track in the scene.\r\n     */\r\n    public id: number = -1;\r\n    /**\r\n     * The list of sounds included in the sound track.\r\n     */\r\n    public soundCollection: Array<Sound>;\r\n\r\n    private _outputAudioNode: Nullable<GainNode>;\r\n    private _scene: Scene;\r\n    private _connectedAnalyser: Analyser;\r\n    private _options: ISoundTrackOptions;\r\n    private _isInitialized = false;\r\n\r\n    /**\r\n     * Creates a new sound track.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-sound-tracks\r\n     * @param scene Define the scene the sound track belongs to\r\n     * @param options\r\n     */\r\n    constructor(scene?: Nullable<Scene>, options: ISoundTrackOptions = {}) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this._scene = scene;\r\n        this.soundCollection = new Array();\r\n        this._options = options;\r\n\r\n        if (!this._options.mainTrack && this._scene.soundTracks) {\r\n            this._scene.soundTracks.push(this);\r\n            this.id = this._scene.soundTracks.length - 1;\r\n        }\r\n    }\r\n\r\n    private _initializeSoundTrackAudioGraph() {\r\n        if (Engine.audioEngine?.canUseWebAudio && Engine.audioEngine.audioContext) {\r\n            this._outputAudioNode = Engine.audioEngine.audioContext.createGain();\r\n            this._outputAudioNode.connect(Engine.audioEngine.masterGain);\r\n\r\n            if (this._options) {\r\n                if (this._options.volume) {\r\n                    this._outputAudioNode.gain.value = this._options.volume;\r\n                }\r\n            }\r\n\r\n            this._isInitialized = true;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Release the sound track and its associated resources\r\n     */\r\n    public dispose(): void {\r\n        if (Engine.audioEngine && Engine.audioEngine.canUseWebAudio) {\r\n            if (this._connectedAnalyser) {\r\n                this._connectedAnalyser.stopDebugCanvas();\r\n            }\r\n            while (this.soundCollection.length) {\r\n                this.soundCollection[0].dispose();\r\n            }\r\n            if (this._outputAudioNode) {\r\n                this._outputAudioNode.disconnect();\r\n            }\r\n            this._outputAudioNode = null;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds a sound to this sound track\r\n     * @param sound define the sound to add\r\n     * @ignoreNaming\r\n     */\r\n    public addSound(sound: Sound): void {\r\n        if (!this._isInitialized) {\r\n            this._initializeSoundTrackAudioGraph();\r\n        }\r\n        if (Engine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            sound.connectToSoundTrackAudioNode(this._outputAudioNode);\r\n        }\r\n        if (sound.soundTrackId) {\r\n            if (sound.soundTrackId === -1) {\r\n                this._scene.mainSoundTrack.removeSound(sound);\r\n            } else if (this._scene.soundTracks) {\r\n                this._scene.soundTracks[sound.soundTrackId].removeSound(sound);\r\n            }\r\n        }\r\n\r\n        this.soundCollection.push(sound);\r\n        sound.soundTrackId = this.id;\r\n    }\r\n\r\n    /**\r\n     * Removes a sound to this sound track\r\n     * @param sound define the sound to remove\r\n     * @ignoreNaming\r\n     */\r\n    public removeSound(sound: Sound): void {\r\n        const index = this.soundCollection.indexOf(sound);\r\n        if (index !== -1) {\r\n            this.soundCollection.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Set a global volume for the full sound track.\r\n     * @param newVolume Define the new volume of the sound track\r\n     */\r\n    public setVolume(newVolume: number): void {\r\n        if (Engine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            this._outputAudioNode.gain.value = newVolume;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to HRTF:\r\n     * Renders a stereo output of higher quality than equalpower — it uses a convolution with measured impulse responses from human subjects.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToHRTF(): void {\r\n        if (Engine.audioEngine?.canUseWebAudio) {\r\n            for (let i = 0; i < this.soundCollection.length; i++) {\r\n                this.soundCollection[i].switchPanningModelToHRTF();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch the panning model to Equal Power:\r\n     * Represents the equal-power panning algorithm, generally regarded as simple and efficient. equalpower is the default value.\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#creating-a-spatial-3d-sound\r\n     */\r\n    public switchPanningModelToEqualPower(): void {\r\n        if (Engine.audioEngine?.canUseWebAudio) {\r\n            for (let i = 0; i < this.soundCollection.length; i++) {\r\n                this.soundCollection[i].switchPanningModelToEqualPower();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Connect the sound track to an audio analyser allowing some amazing\r\n     * synchronization between the sounds/music and your visualization (VuMeter for instance).\r\n     * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic#using-the-analyser\r\n     * @param analyser The analyser to connect to the engine\r\n     */\r\n    public connectToAnalyser(analyser: Analyser): void {\r\n        if (this._connectedAnalyser) {\r\n            this._connectedAnalyser.stopDebugCanvas();\r\n        }\r\n        this._connectedAnalyser = analyser;\r\n        if (Engine.audioEngine?.canUseWebAudio && this._outputAudioNode) {\r\n            this._outputAudioNode.disconnect();\r\n            this._connectedAnalyser.connectAudioNodes(this._outputAudioNode, Engine.audioEngine.masterGain);\r\n        }\r\n    }\r\n}\r\n"]}