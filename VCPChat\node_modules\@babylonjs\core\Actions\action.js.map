{"version": 3, "file": "action.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/action.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAErD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAiElD;;;GAGG;AACH,MAAM,OAAO,MAAM;IAsBf;;;;OAIG;IACH;IACI,8DAA8D;IACvD,cAAmB,EAC1B,SAAqB;QADd,mBAAc,GAAd,cAAc,CAAK;QAZ9B;;WAEG;QACI,8BAAyB,GAAG,IAAI,UAAU,EAAU,CAAC;QAYxD,IAAI,cAAc,CAAC,SAAS,EAAE;YAC1B,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;YACtC,IAAI,CAAC,iBAAiB,GAAG,cAAc,CAAC,SAAS,CAAC;SACrD;aAAM,IAAI,cAAc,CAAC,OAAO,EAAE;YAC/B,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC;SACzC;aAAM;YACH,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC;SACjC;QAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;QAC9B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,QAAQ,KAAU,CAAC;IAE1B;;;OAGG;IACI,mBAAmB;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,mBAAmB,CAAC,KAAU;QACjC,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;IACnC,CAAC;IAED;;;OAGG;IACI,iCAAiC;QACpC,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC,WAAW,EAAE,CAAC;QAErE,wDAAwD;QACxD,IAAI,SAAS,CAAC,aAAa,KAAK,eAAe,EAAE;YAC7C,SAAS,CAAC,aAAa,GAAG,eAAe,CAAC;YAC1C,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC;SAClD;QAED,OAAO,SAAS,CAAC,cAAc,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,eAAe,CAAC,GAAiB;QACpC,MAAM,gBAAgB,GAAG,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAClE,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO;SACV;QAED,IAAI,CAAC,yBAAyB,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACrD,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAClC,CAAC;IAED;;;OAGG;IACH,6DAA6D;IACtD,OAAO,CAAC,GAAiB,IAAS,CAAC;IAE1C;;OAEG;IACI,sBAAsB;QACzB,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,cAAc,EAAE;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;aACtE;YAED,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;SAC1D;aAAM;YACH,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;SACjC;IACL,CAAC;IAED;;;;;OAKG;IACI,IAAI,CAAC,MAAc;QACtB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QAErB,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC5C,MAAM,CAAC,QAAQ,EAAE,CAAC;QAElB,OAAO,MAAM,CAAC;IAClB,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,YAAoB;QACpC,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,MAAW,EAAE,YAAoB;QACxD,OAAO,IAAI,CAAC,cAAc,CAAC,mBAAmB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;IACzE,CAAC;IAED;;;;OAIG;IACH,6DAA6D;IACtD,SAAS,CAAC,MAAW,IAAQ,CAAC;IAErC;;;OAGG;IACO,UAAU,CAAC,gBAAqB,EAAE,MAAY;QACpD,MAAM,mBAAmB,GAAQ;YAC7B,IAAI,EAAE,CAAC;YACP,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE,gBAAgB,CAAC,IAAI;YAC3B,UAAU,EAAE,gBAAgB,CAAC,UAAU,IAAI,EAAE;SAChD,CAAC;QAEF,kBAAkB;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,mBAAmB,CAAC,CAAC;SAC9C;QAED,kCAAkC;QAClC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,MAAM,mBAAmB,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YACxD,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAEvD,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;aAC7C;YACD,OAAO,mBAAmB,CAAC;SAC9B;QAED,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SAC7C;QACD,OAAO,mBAAmB,CAAC;IAC/B,CAAC;;AAED;;;GAGG;AACW,8BAAuB,GAAG,CAAC,KAAU,EAAU,EAAE;IAC3D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;KAC3B;IAED,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;QAC5B,OAAO,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC;KACnC;IAED,IAAI,KAAK,YAAY,OAAO,EAAE;QAC1B,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;KACnC;IACD,IAAI,KAAK,YAAY,OAAO,EAAE;QAC1B,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;KACpD;IAED,IAAI,KAAK,YAAY,MAAM,EAAE;QACzB,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;KACpD;IACD,IAAI,KAAK,YAAY,MAAM,EAAE;QACzB,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;KACrE;IAED,OAAO,KAAK,CAAC,CAAC,SAAS;AAC3B,CAAC,CAAC;AAEF;;;GAGG;AACW,yBAAkB,GAAG,CAAC,MAA+B,EAAE,EAAE;IACnE,OAAO;QACH,IAAI,EAAE,QAAQ;QACd,UAAU,EAAS,MAAO,CAAC,OAAO;YAC9B,CAAC,CAAC,gBAAgB;YAClB,CAAC,CAAS,MAAO,CAAC,QAAQ;gBAC1B,CAAC,CAAC,iBAAiB;gBACnB,CAAC,CAAU,MAAO,CAAC,SAAS;oBAC5B,CAAC,CAAC,kBAAkB;oBACpB,CAAC,CAAY,MAAO,CAAC,WAAW;wBAChC,CAAC,CAAC,oBAAoB;wBACtB,CAAC,CAAC,iBAAiB;QACvB,KAAK,EAAU,MAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAQ,MAAO,CAAC,IAAI;KAClE,CAAC;AACN,CAAC,CAAC;AAGN,aAAa,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC", "sourcesContent": ["import { Observable } from \"../Misc/observable\";\r\nimport { Vector2, Vector3 } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport type { Condition } from \"./condition\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { AbstractActionManager } from \"./abstractActionManager\";\r\nimport type { Nullable } from \"../types\";\r\nimport type { Material } from \"../Materials/material\";\r\n\r\ndeclare type Scene = import(\"../scene\").Scene;\r\ndeclare type ActionManager = import(\"./actionManager\").ActionManager;\r\ndeclare type ActionEvent = import(\"./actionEvent\").ActionEvent;\r\ndeclare type Mesh = import(\"../Meshes/mesh\").Mesh;\r\ndeclare type Light = import(\"../Lights/light\").Light;\r\ndeclare type Camera = import(\"../Cameras/camera\").Camera;\r\ndeclare type Node = import(\"../node\").Node;\r\n\r\n/**\r\n * Interface used to define Action\r\n */\r\nexport interface IAction {\r\n    /**\r\n     * Trigger for the action\r\n     */\r\n    trigger: number;\r\n\r\n    /** Options of the trigger */\r\n    triggerOptions: any;\r\n\r\n    /**\r\n     * Gets the trigger parameters\r\n     * @returns the trigger parameters\r\n     */\r\n    getTriggerParameter(): any;\r\n\r\n    /**\r\n     * Internal only - executes current action event\r\n     * @internal\r\n     */\r\n    _executeCurrent(evt?: ActionEvent): void;\r\n\r\n    /**\r\n     * Serialize placeholder for child classes\r\n     * @param parent of child\r\n     * @returns the serialized object\r\n     */\r\n    serialize(parent: any): any;\r\n\r\n    /**\r\n     * Internal only\r\n     * @internal\r\n     */\r\n    _prepare(): void;\r\n\r\n    /**\r\n     * Internal only - manager for action\r\n     * @internal\r\n     */\r\n    _actionManager: Nullable<AbstractActionManager>;\r\n\r\n    /**\r\n     * Adds action to chain of actions, may be a DoNothingAction\r\n     * @param action defines the next action to execute\r\n     * @returns The action passed in\r\n     * @see https://www.babylonjs-playground.com/#1T30HR#0\r\n     */\r\n    then(action: IAction): IAction;\r\n}\r\n\r\n/**\r\n * The action to be carried out following a trigger\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions#available-actions\r\n */\r\nexport class Action implements IAction {\r\n    /**\r\n     * Trigger for the action\r\n     */\r\n    public trigger: number;\r\n\r\n    /**\r\n     * Internal only - manager for action\r\n     * @internal\r\n     */\r\n    public _actionManager: ActionManager;\r\n\r\n    private _nextActiveAction: Action;\r\n    private _child: Action;\r\n    private _condition?: Condition;\r\n    private _triggerParameter: any;\r\n\r\n    /**\r\n     * An event triggered prior to action being executed.\r\n     */\r\n    public onBeforeExecuteObservable = new Observable<Action>();\r\n\r\n    /**\r\n     * Creates a new Action\r\n     * @param triggerOptions the trigger, with or without parameters, for the action\r\n     * @param condition an optional determinant of action\r\n     */\r\n    constructor(\r\n        /** the trigger, with or without parameters, for the action */\r\n        public triggerOptions: any,\r\n        condition?: Condition\r\n    ) {\r\n        if (triggerOptions.parameter) {\r\n            this.trigger = triggerOptions.trigger;\r\n            this._triggerParameter = triggerOptions.parameter;\r\n        } else if (triggerOptions.trigger) {\r\n            this.trigger = triggerOptions.trigger;\r\n        } else {\r\n            this.trigger = triggerOptions;\r\n        }\r\n\r\n        this._nextActiveAction = this;\r\n        this._condition = condition;\r\n    }\r\n\r\n    /**\r\n     * Internal only\r\n     * @internal\r\n     */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Gets the trigger parameter\r\n     * @returns the trigger parameter\r\n     */\r\n    public getTriggerParameter(): any {\r\n        return this._triggerParameter;\r\n    }\r\n\r\n    /**\r\n     * Sets the trigger parameter\r\n     * @param value defines the new trigger parameter\r\n     */\r\n    public setTriggerParameter(value: any) {\r\n        this._triggerParameter = value;\r\n    }\r\n\r\n    /**\r\n     * Internal only - Returns if the current condition allows to run the action\r\n     * @internal\r\n     */\r\n    public _evaluateConditionForCurrentFrame(): boolean {\r\n        const condition = this._condition;\r\n        if (!condition) {\r\n            return true;\r\n        }\r\n\r\n        const currentRenderId = this._actionManager.getScene().getRenderId();\r\n\r\n        // We cache the current evaluation for the current frame\r\n        if (condition._evaluationId !== currentRenderId) {\r\n            condition._evaluationId = currentRenderId;\r\n            condition._currentResult = condition.isValid();\r\n        }\r\n\r\n        return condition._currentResult;\r\n    }\r\n\r\n    /**\r\n     * Internal only - executes current action event\r\n     * @internal\r\n     */\r\n    public _executeCurrent(evt?: ActionEvent): void {\r\n        const isConditionValid = this._evaluateConditionForCurrentFrame();\r\n        if (!isConditionValid) {\r\n            return;\r\n        }\r\n\r\n        this.onBeforeExecuteObservable.notifyObservers(this);\r\n        this._nextActiveAction.execute(evt);\r\n\r\n        this.skipToNextActiveAction();\r\n    }\r\n\r\n    /**\r\n     * Execute placeholder for child classes\r\n     * @param evt optional action event\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public execute(evt?: ActionEvent): void {}\r\n\r\n    /**\r\n     * Skips to next active action\r\n     */\r\n    public skipToNextActiveAction(): void {\r\n        if (this._nextActiveAction._child) {\r\n            if (!this._nextActiveAction._child._actionManager) {\r\n                this._nextActiveAction._child._actionManager = this._actionManager;\r\n            }\r\n\r\n            this._nextActiveAction = this._nextActiveAction._child;\r\n        } else {\r\n            this._nextActiveAction = this;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds action to chain of actions, may be a DoNothingAction\r\n     * @param action defines the next action to execute\r\n     * @returns The action passed in\r\n     * @see https://www.babylonjs-playground.com/#1T30HR#0\r\n     */\r\n    public then(action: Action): Action {\r\n        this._child = action;\r\n\r\n        action._actionManager = this._actionManager;\r\n        action._prepare();\r\n\r\n        return action;\r\n    }\r\n\r\n    /**\r\n     * Internal only\r\n     * @internal\r\n     */\r\n    public _getProperty(propertyPath: string): string {\r\n        return this._actionManager._getProperty(propertyPath);\r\n    }\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public _getEffectiveTarget(target: any, propertyPath: string): any {\r\n        return this._actionManager._getEffectiveTarget(target, propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Serialize placeholder for child classes\r\n     * @param parent of child\r\n     * @returns the serialized object\r\n     */\r\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n    public serialize(parent: any): any {}\r\n\r\n    /**\r\n     * Internal only called by serialize\r\n     * @internal\r\n     */\r\n    protected _serialize(serializedAction: any, parent?: any): any {\r\n        const serializationObject: any = {\r\n            type: 1,\r\n            children: [],\r\n            name: serializedAction.name,\r\n            properties: serializedAction.properties || [],\r\n        };\r\n\r\n        // Serialize child\r\n        if (this._child) {\r\n            this._child.serialize(serializationObject);\r\n        }\r\n\r\n        // Check if \"this\" has a condition\r\n        if (this._condition) {\r\n            const serializedCondition = this._condition.serialize();\r\n            serializedCondition.children.push(serializationObject);\r\n\r\n            if (parent) {\r\n                parent.children.push(serializedCondition);\r\n            }\r\n            return serializedCondition;\r\n        }\r\n\r\n        if (parent) {\r\n            parent.children.push(serializationObject);\r\n        }\r\n        return serializationObject;\r\n    }\r\n\r\n    /**\r\n     * Internal only\r\n     * @internal\r\n     */\r\n    public static _SerializeValueAsString = (value: any): string => {\r\n        if (typeof value === \"number\") {\r\n            return value.toString();\r\n        }\r\n\r\n        if (typeof value === \"boolean\") {\r\n            return value ? \"true\" : \"false\";\r\n        }\r\n\r\n        if (value instanceof Vector2) {\r\n            return value.x + \", \" + value.y;\r\n        }\r\n        if (value instanceof Vector3) {\r\n            return value.x + \", \" + value.y + \", \" + value.z;\r\n        }\r\n\r\n        if (value instanceof Color3) {\r\n            return value.r + \", \" + value.g + \", \" + value.b;\r\n        }\r\n        if (value instanceof Color4) {\r\n            return value.r + \", \" + value.g + \", \" + value.b + \", \" + value.a;\r\n        }\r\n\r\n        return value; // string\r\n    };\r\n\r\n    /**\r\n     * Internal only\r\n     * @internal\r\n     */\r\n    public static _GetTargetProperty = (target: Scene | Node | Material) => {\r\n        return {\r\n            name: \"target\",\r\n            targetType: (<Mesh>target)._isMesh\r\n                ? \"MeshProperties\"\r\n                : (<Light>target)._isLight\r\n                ? \"LightProperties\"\r\n                : (<Camera>target)._isCamera\r\n                ? \"CameraProperties\"\r\n                : (<Material>target)._isMaterial\r\n                ? \"MaterialProperties\"\r\n                : \"SceneProperties\",\r\n            value: (<Scene>target)._isScene ? \"Scene\" : (<Node>target).name,\r\n        };\r\n    };\r\n}\r\n\r\nRegisterClass(\"BABYLON.Action\", Action);\r\n"]}