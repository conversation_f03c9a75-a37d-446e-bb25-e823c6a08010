{"name": "tiny-async-pool", "version": "1.3.0", "description": "Run multiple promise-returning & async functions with limited concurrency using native ES6/ES7", "main": "dist/node.js", "files": ["LICENSE-MIT", "dist", "lib"], "scripts": {"prettier": "prettier --write '{lib,test}/**/*.js'", "test": "eslint src && NODE_ENV=development mocha"}, "repository": {"type": "git", "url": "**************:rxaviers/async-pool.git"}, "keywords": ["race", "concurrency", "promise", "async", "es6", "es7"], "author": "<PERSON>", "license": "MIT", "devDependencies": {"chai": "^4.1.2", "chai-as-promised": "^7.1.1", "eslint": "^4.17.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.0", "mocha": "^5.0.0", "prettier": "^1.10.2"}, "dependencies": {"semver": "^5.5.0"}}