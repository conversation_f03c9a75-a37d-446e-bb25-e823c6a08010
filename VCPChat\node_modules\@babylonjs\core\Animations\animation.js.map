{"version": 3, "file": "animation.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AACxF,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAI9C,OAAO,EAAE,mBAAmB,EAAE,MAAM,oBAAoB,CAAC;AACzD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAElD,OAAO,EAAE,yBAAyB,EAAE,MAAM,gBAAgB,CAAC;AAC3D,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,OAAO,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC;AAE/B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAC;AAChD,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AAKjD;;GAEG;AACH,gEAAgE;AAChE,MAAM,OAAO,gBAAgB;CAO5B;AAED;;GAEG;AACH,MAAM,OAAO,SAAS;IA2DlB;;OAEG;IACI,MAAM,CAAC,iBAAiB,CAC3B,IAAY,EACZ,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B;QAE/B,IAAI,QAAQ,GAAG,SAAS,CAAC;QAEzB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;YAC5C,QAAQ,GAAG,SAAS,CAAC,mBAAmB,CAAC;SAC5C;aAAM,IAAI,IAAI,YAAY,UAAU,EAAE;YACnC,QAAQ,GAAG,SAAS,CAAC,wBAAwB,CAAC;SACjD;aAAM,IAAI,IAAI,YAAY,OAAO,EAAE;YAChC,QAAQ,GAAG,SAAS,CAAC,qBAAqB,CAAC;SAC9C;aAAM,IAAI,IAAI,YAAY,OAAO,EAAE;YAChC,QAAQ,GAAG,SAAS,CAAC,qBAAqB,CAAC;SAC9C;aAAM,IAAI,IAAI,YAAY,MAAM,EAAE;YAC/B,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;SAC7C;aAAM,IAAI,IAAI,YAAY,MAAM,EAAE;YAC/B,QAAQ,GAAG,SAAS,CAAC,oBAAoB,CAAC;SAC7C;aAAM,IAAI,IAAI,YAAY,IAAI,EAAE;YAC7B,QAAQ,GAAG,SAAS,CAAC,kBAAkB,CAAC;SAC3C;QAED,IAAI,QAAQ,IAAI,SAAS,EAAE;YACvB,OAAO,IAAI,CAAC;SACf;QAED,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;QAE1F,MAAM,IAAI,GAAyB;YAC/B,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE;YACzB,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,EAAE,EAAE;SACnC,CAAC;QACF,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,cAAc,KAAK,SAAS,EAAE;YAC9B,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;SAC/C;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;OAOG;IACI,MAAM,CAAC,eAAe,CAAC,QAAgB,EAAE,aAAqB,EAAE,cAAsB,EAAE,cAA8B;QACzH,MAAM,SAAS,GAAc,IAAI,SAAS,CAAC,QAAQ,GAAG,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,aAAa,EAAE,SAAS,CAAC,0BAA0B,CAAC,CAAC;QAElJ,SAAS,CAAC,iBAAiB,CAAC,cAAc,CAAC,CAAC;QAE5C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,MAAM,CAAC,uBAAuB,CACjC,IAAY,EACZ,MAAW,EACX,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B,EAC3B,KAAa;QAEb,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,IAAI,MAAM,CAAC,QAAQ,EAAE;YACjB,KAAK,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC;SAC7B;QAED,IAAI,CAAC,KAAK,EAAE;YACR,OAAO,IAAI,CAAC;SACf;QAED,OAAO,KAAK,CAAC,oBAAoB,CAAC,MAAM,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACzH,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,MAAM,CAAC,gCAAgC,CAC1C,IAAY,EACZ,IAAU,EACV,qBAA8B,EAC9B,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B;QAE3B,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9B,OAAO,KAAK,CAAC,6BAA6B,CAAC,IAAI,EAAE,qBAAqB,EAAE,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IACvJ,CAAC;IAED;;;;;;;;;;;;;OAaG;IACI,MAAM,CAAC,4BAA4B,CACtC,IAAY,EACZ,IAAU,EACV,cAAsB,EACtB,cAAsB,EACtB,UAAkB,EAClB,IAAS,EACT,EAAO,EACP,QAAiB,EACjB,cAA+B,EAC/B,cAA2B;QAE3B,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC;QAEpI,IAAI,CAAC,SAAS,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAEhC,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,QAAQ,KAAK,CAAC,EAAE,GAAG,EAAE,cAAc,CAAC,CAAC;IAC9G,CAAC;IAED;;;;;;;;OAQG;IACI,MAAM,CAAC,qBAAqB,CAAC,eAA0B,EAAE,cAAc,GAAG,CAAC,EAAE,KAAc,EAAE,aAAa,GAAG,KAAK,EAAE,UAAmB;QAC1I,IAAI,SAAS,GAAG,eAAe,CAAC;QAEhC,IAAI,aAAa,EAAE;YACf,SAAS,GAAG,eAAe,CAAC,KAAK,EAAE,CAAC;YACpC,SAAS,CAAC,IAAI,GAAG,UAAU,IAAI,SAAS,CAAC,IAAI,CAAC;SACjD;QAED,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE;YACzB,OAAO,SAAS,CAAC;SACpB;QAED,cAAc,GAAG,cAAc,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,UAAU,GAAG;YACf,cAAc,EAAE,QAAQ,CAAC,KAAK;YAC9B,iBAAiB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACxC,mBAAmB,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YAC7C,gBAAgB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YACvC,WAAW,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;YAClC,aAAa,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;YACvC,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;SACpC,CAAC;QACF,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC1B,IAAI,EAAE,GAAG,OAAO,CAAC,KAAK,CAAC;QACvB,IAAI,KAAK,EAAE;YACP,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAE7C,IAAI,UAAU,EAAE;gBACZ,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;gBACvB,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC;aACtB;SACJ;QACD,IAAI,YAAY,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;QAC3C,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,KAAK,EAAE,CAAC;QAEtC,kCAAkC;QAClC,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAChE,cAAc,GAAG,IAAI,CAAC;SACzB;QAED,yEAAyE;aACpE,IAAI,cAAc,IAAI,QAAQ,CAAC,KAAK,EAAE;YACvC,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACrD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAChE,cAAc,GAAG,IAAI,CAAC;SACzB;QAED,sEAAsE;aACjE,IAAI,cAAc,IAAI,OAAO,CAAC,KAAK,EAAE;YACtC,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;YACpD,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;YAChE,cAAc,GAAG,IAAI,CAAC;SACzB;QAED,qDAAqD;QACrD,IAAI,KAAK,GAAG,CAAC,CAAC;QACd,OAAO,CAAC,cAAc,IAAI,CAAC,YAAY,IAAI,CAAC,CAAC,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;YAC5F,MAAM,UAAU,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAC1C,MAAM,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;YAE3C,yEAAyE;YACzE,IAAI,CAAC,cAAc,IAAI,cAAc,IAAI,UAAU,CAAC,KAAK,IAAI,cAAc,IAAI,OAAO,CAAC,KAAK,EAAE;gBAC1F,IAAI,KAAK,CAAC;gBAEV,IAAI,cAAc,KAAK,UAAU,CAAC,KAAK,EAAE;oBACrC,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;iBACpD;qBAAM,IAAI,cAAc,KAAK,OAAO,CAAC,KAAK,EAAE;oBACzC,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;iBACjD;qBAAM;oBACH,MAAM,cAAc,GAAG;wBACnB,GAAG,EAAE,KAAK;wBACV,WAAW,EAAE,CAAC;wBACd,QAAQ,EAAE,IAAI,CAAC,0BAA0B;qBAC5C,CAAC;oBACF,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;iBAClE;gBAED,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC;gBAChE,cAAc,GAAG,IAAI,CAAC;aACzB;YAED,kEAAkE;YAClE,IAAI,CAAC,YAAY,IAAI,IAAI,IAAI,UAAU,CAAC,KAAK,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;gBACpE,IAAI,IAAI,KAAK,UAAU,CAAC,KAAK,EAAE;oBAC3B,UAAU,GAAG,KAAK,CAAC;iBACtB;qBAAM,IAAI,IAAI,KAAK,OAAO,CAAC,KAAK,EAAE;oBAC/B,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;iBAC1B;qBAAM;oBACH,MAAM,cAAc,GAAG;wBACnB,GAAG,EAAE,KAAK;wBACV,WAAW,EAAE,CAAC;wBACd,QAAQ,EAAE,IAAI,CAAC,0BAA0B;qBAC5C,CAAC;oBACF,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC;oBAC3D,MAAM,GAAG,GAAkB;wBACvB,KAAK,EAAE,IAAI;wBACX,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;qBAC7C,CAAC;oBACF,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC1C,UAAU,GAAG,KAAK,GAAG,CAAC,CAAC;iBAC1B;gBAED,YAAY,GAAG,IAAI,CAAC;aACvB;YAED,gEAAgE;YAChE,IAAI,CAAC,UAAU,IAAI,EAAE,IAAI,UAAU,CAAC,KAAK,IAAI,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;gBAC9D,IAAI,EAAE,KAAK,UAAU,CAAC,KAAK,EAAE;oBACzB,QAAQ,GAAG,KAAK,CAAC;iBACpB;qBAAM,IAAI,EAAE,KAAK,OAAO,CAAC,KAAK,EAAE;oBAC7B,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC;iBACxB;qBAAM;oBACH,MAAM,cAAc,GAAG;wBACnB,GAAG,EAAE,KAAK;wBACV,WAAW,EAAE,CAAC;wBACd,QAAQ,EAAE,IAAI,CAAC,0BAA0B;qBAC5C,CAAC;oBACF,MAAM,KAAK,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC;oBACzD,MAAM,GAAG,GAAkB;wBACvB,KAAK,EAAE,EAAE;wBACT,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;qBAC7C,CAAC;oBACF,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC1C,QAAQ,GAAG,KAAK,GAAG,CAAC,CAAC;iBACxB;gBAED,UAAU,GAAG,IAAI,CAAC;aACrB;YAED,KAAK,EAAE,CAAC;SACX;QAED,2BAA2B;QAC3B,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,wBAAwB,EAAE;YAC3D,UAAU,CAAC,cAAc,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;SAC5D;QAED,gDAAgD;aAC3C,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,oBAAoB,EAAE;YAC5D,UAAU,CAAC,cAAc,CAAC,SAAS,CAAC,UAAU,CAAC,gBAAgB,EAAE,UAAU,CAAC,mBAAmB,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;YAC/H,UAAU,CAAC,mBAAmB,CAAC,SAAS,EAAE,CAAC,gBAAgB,EAAE,CAAC;SACjE;QAED,0DAA0D;QAC1D,KAAK,KAAK,GAAG,UAAU,EAAE,KAAK,IAAI,QAAQ,EAAE,KAAK,EAAE,EAAE;YACjD,MAAM,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YAEnC,yGAAyG;YACzG,IAAI,KAAK,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,mBAAmB,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,EAAE;gBAC/F,SAAS;aACZ;YAED,QAAQ,SAAS,CAAC,QAAQ,EAAE;gBACxB,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;oBAC7F,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;oBACrE,UAAU,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;oBACjE,UAAU,CAAC,mBAAmB,CAAC,aAAa,CAAC,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;oBACjG,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU,EAAE,UAAU,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBACxG,MAAM;gBAEV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC9D,MAAM;gBAEV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,KAAK,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;oBAC9D,MAAM;gBAEV,KAAK,SAAS,CAAC,kBAAkB;oBAC7B,GAAG,CAAC,KAAK,CAAC,KAAK,IAAI,UAAU,CAAC,cAAc,CAAC,KAAK,CAAC;oBACnD,GAAG,CAAC,KAAK,CAAC,MAAM,IAAI,UAAU,CAAC,cAAc,CAAC,MAAM,CAAC;oBACrD,MAAM;gBAEV;oBACI,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC,cAAc,CAAC;aAC9C;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;;;;;;;;OAWG;IACI,MAAM,CAAC,YAAY,CACtB,QAAgB,EAChB,WAAgB,EAChB,IAAS,EACT,KAAY,EACZ,SAAiB,EACjB,UAAqB,EACrB,QAAgB,EAChB,iBAAuC,IAAI;QAE3C,IAAI,QAAQ,IAAI,CAAC,EAAE;YACf,IAAI,CAAC,QAAQ,CAAC,GAAG,WAAW,CAAC;YAC7B,IAAI,cAAc,EAAE;gBAChB,cAAc,EAAE,CAAC;aACpB;YACD,OAAO,IAAI,CAAC;SACf;QAED,MAAM,QAAQ,GAAW,SAAS,GAAG,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;QAEvD,UAAU,CAAC,OAAO,CAAC;YACf;gBACI,KAAK,EAAE,CAAC;gBACR,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;aACxE;YACD;gBACI,KAAK,EAAE,QAAQ;gBACf,KAAK,EAAE,WAAW;aACrB;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;SACxB;QAED,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEjC,MAAM,SAAS,GAAe,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;QAC7E,SAAS,CAAC,cAAc,GAAG,cAAc,CAAC;QAC1C,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,IAAW,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,IAAW,2BAA2B;QAClC,KAAK,MAAM,gBAAgB,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACpD,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,EAAE;gBAC/B,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;;;;;OAQG;IACH;IACI,2BAA2B;IACpB,IAAY;IACnB,yBAAyB;IAClB,cAAsB;IAC7B,4CAA4C;IACrC,cAAsB;IAC7B,oCAAoC;IAC7B,QAAgB;IACvB,oCAAoC;IAC7B,QAAiB;IACxB,6CAA6C;IACtC,cAAwB;QAVxB,SAAI,GAAJ,IAAI,CAAQ;QAEZ,mBAAc,GAAd,cAAc,CAAQ;QAEtB,mBAAc,GAAd,cAAc,CAAQ;QAEtB,aAAQ,GAAR,QAAQ,CAAQ;QAEhB,aAAQ,GAAR,QAAQ,CAAS;QAEjB,mBAAc,GAAd,cAAc,CAAU;QAjgBnC;;WAEG;QACK,oBAAe,GAA8B,IAAI,CAAC;QAE1D;;WAEG;QACI,uBAAkB,GAAG,IAAI,KAAK,EAAoB,CAAC;QAE1D;;WAEG;QACK,YAAO,GAAG,IAAI,KAAK,EAAkB,CAAC;QAO9C;;WAEG;QACI,kBAAa,GAAG,IAAI,CAAC;QAE5B;;WAEG;QACK,YAAO,GAAiD,EAAE,CAAC;QAue/D,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,CAAC,QAAQ,CAAC;QACtF,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,kBAAkB,EAAE,CAAC;IACnD,CAAC;IAED,UAAU;IACV;;;;OAIG;IACI,QAAQ,CAAC,WAAqB;QACjC,IAAI,GAAG,GAAG,QAAQ,GAAG,IAAI,CAAC,IAAI,GAAG,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACtE,GAAG,IAAI,cAAc,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzG,GAAG,IAAI,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAC/D,GAAG,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAClF,IAAI,WAAW,EAAE;YACb,GAAG,IAAI,aAAa,CAAC;YACrB,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC7B,IAAI,KAAK,EAAE;oBACP,GAAG,IAAI,IAAI,CAAC;oBACZ,KAAK,GAAG,KAAK,CAAC;iBACjB;gBACD,GAAG,IAAI,IAAI,CAAC;aACf;YACD,GAAG,IAAI,GAAG,CAAC;SACd;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,QAAQ,CAAC,KAAqB;QACjC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACzB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,YAAY,CAAC,KAAa;QAC7B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE;gBACrC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBAC9B,KAAK,EAAE,CAAC;aACX;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACI,WAAW,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU;QACrD,yEAAyE;QACzE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;SAC3D;IACL,CAAC;IAED;;;;OAIG;IACI,WAAW,CAAC,IAAY,EAAE,YAAY,GAAG,IAAI;QAChD,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,YAAY,EAAE;YACd,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;YACxB,MAAM,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;YAEpB,6DAA6D;YAC7D,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,EAAE;gBACnD,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,EAAE,EAAE;oBAC9D,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;iBAC7B;aACJ;SACJ;QACD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,mDAAmD;IAClF,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,IAAY;QACxB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,OAAO,IAAI,CAAC,KAAK,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,eAAe;QAClB,IAAI,GAAG,GAAG,CAAC,CAAC;QAEZ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;YAC7D,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;gBAC7B,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;aAC/B;SACJ;QACD,OAAO,GAAG,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,iBAAiB;QACpB,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IAED;;;OAGG;IACI,iBAAiB,CAAC,cAAyC;QAC9D,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IAC1C,CAAC;IAED;;;;;;OAMG;IACI,wBAAwB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QAClF,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACI,oCAAoC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB;QACrI,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG;IACI,6BAA6B,CAAC,UAAsB,EAAE,QAAoB,EAAE,QAAgB;QAC/F,OAAO,UAAU,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC5D,CAAC;IAED;;;;;;;;OAQG;IACI,yCAAyC,CAAC,UAAsB,EAAE,UAAsB,EAAE,QAAoB,EAAE,SAAqB,EAAE,QAAgB;QAC1J,OAAO,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC,SAAS,EAAE,CAAC;IACjG,CAAC;IAED;;;;;;OAMG;IACI,0BAA0B,CAAC,UAAmB,EAAE,QAAiB,EAAE,QAAgB;QACtF,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;OAQG;IACI,sCAAsC,CAAC,UAAmB,EAAE,UAAmB,EAAE,QAAiB,EAAE,SAAkB,EAAE,QAAgB;QAC3I,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;;OAMG;IACI,0BAA0B,CAAC,UAAmB,EAAE,QAAiB,EAAE,QAAgB;QACtF,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;;OAQG;IACI,sCAAsC,CAAC,UAAmB,EAAE,UAAmB,EAAE,QAAiB,EAAE,SAAkB,EAAE,QAAgB;QAC3I,OAAO,OAAO,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IAClF,CAAC;IAED;;;;;;OAMG;IACI,uBAAuB,CAAC,UAAgB,EAAE,QAAc,EAAE,QAAgB;QAC7E,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;OAMG;IACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QACnF,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACI,qCAAqC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB;QACtI,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG;IACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB;QACnF,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;;;;;;OAQG;IACI,qCAAqC,CAAC,UAAkB,EAAE,UAAkB,EAAE,QAAgB,EAAE,SAAiB,EAAE,QAAgB;QACtI,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjF,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,KAAU;QAC1B,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC7B,OAAO,KAAK,EAAE,CAAC;SAClB;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,YAAoB;QAChC,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;YACnC,GAAG,EAAE,CAAC;YACN,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,SAAS,CAAC,0BAA0B;SACjD,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,YAAoB,EAAE,KAAuB;QAC7D,IAAI,KAAK,CAAC,QAAQ,KAAK,SAAS,CAAC,0BAA0B,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC,EAAE;YAClF,OAAO,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC;SAC3F;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;QACxB,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAE/B,IAAI,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC;QAEpB,OAAO,GAAG,IAAI,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE;YAC/C,EAAE,GAAG,CAAC;SACT;QAED,OAAO,GAAG,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,IAAI,YAAY,IAAI,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;YACrE,EAAE,GAAG,CAAC;SACT;QAED,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC;QAEhB,IAAI,GAAG,GAAG,CAAC,EAAE;YACT,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SAC3C;aAAM,IAAI,GAAG,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;SACxD;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QACjD,IAAI,QAAQ,CAAC,aAAa,KAAK,yBAAyB,CAAC,IAAI,EAAE;YAC3D,IAAI,MAAM,CAAC,KAAK,GAAG,YAAY,EAAE;gBAC7B,OAAO,UAAU,CAAC;aACrB;iBAAM;gBACH,OAAO,QAAQ,CAAC;aACnB;SACJ;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,KAAK,SAAS,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC;QACvF,MAAM,UAAU,GAAG,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAEjD,6EAA6E;QAC7E,IAAI,QAAQ,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC;QAE5D,sDAAsD;QACtD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAChD,IAAI,cAAc,KAAK,IAAI,EAAE;YACzB,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5C;QAED,QAAQ,IAAI,CAAC,QAAQ,EAAE;YACnB,QAAQ;YACR,KAAK,SAAS,CAAC,mBAAmB,CAAC,CAAC;gBAChC,MAAM,UAAU,GAAG,UAAU;oBACzB,CAAC,CAAC,IAAI,CAAC,oCAAoC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,GAAG,UAAU,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,GAAG,UAAU,EAAE,QAAQ,CAAC;oBAC5I,CAAC,CAAC,IAAI,CAAC,wBAAwB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACpE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,UAAU,CAAC;oBACtB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;iBACjE;gBACD,MAAM;aACT;YACD,aAAa;YACb,KAAK,SAAS,CAAC,wBAAwB,CAAC,CAAC;gBACrC,MAAM,SAAS,GAAG,UAAU;oBACxB,CAAC,CAAC,IAAI,CAAC,yCAAyC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;oBAC3J,CAAC,CAAC,IAAI,CAAC,6BAA6B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACzE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC;oBACrB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBAC/E;gBAED,OAAO,SAAS,CAAC;aACpB;YACD,UAAU;YACV,KAAK,SAAS,CAAC,qBAAqB,CAAC,CAAC;gBAClC,MAAM,SAAS,GAAG,UAAU;oBACxB,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;oBACxJ,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACtE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC;oBACrB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBACxE;gBACD,MAAM;aACT;YACD,UAAU;YACV,KAAK,SAAS,CAAC,qBAAqB,CAAC,CAAC;gBAClC,MAAM,SAAS,GAAG,UAAU;oBACxB,CAAC,CAAC,IAAI,CAAC,sCAAsC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;oBACxJ,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACtE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC;oBACrB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBACxE;gBACD,MAAM;aACT;YACD,OAAO;YACP,KAAK,SAAS,CAAC,kBAAkB,CAAC,CAAC;gBAC/B,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;oBACxE,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBAC3H;gBACD,MAAM;aACT;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACjC,MAAM,WAAW,GAAG,UAAU;oBAC1B,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;oBACvJ,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACrE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,WAAW,CAAC;oBACvB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBAC1E;gBACD,MAAM;aACT;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACjC,MAAM,WAAW,GAAG,UAAU;oBAC1B,CAAC,CAAC,IAAI,CAAC,qCAAqC,CAAC,UAAU,EAAE,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE,QAAQ,CAAC;oBACvJ,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACrE,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,WAAW,CAAC;oBACvB,KAAK,SAAS,CAAC,0BAA0B;wBACrC,OAAO,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;iBAC1E;gBACD,MAAM;aACT;YACD,SAAS;YACT,KAAK,SAAS,CAAC,oBAAoB,CAAC,CAAC;gBACjC,QAAQ,KAAK,CAAC,QAAQ,EAAE;oBACpB,KAAK,SAAS,CAAC,uBAAuB,CAAC;oBACvC,KAAK,SAAS,CAAC,0BAA0B,CAAC,CAAC;wBACvC,IAAI,SAAS,CAAC,0BAA0B,EAAE;4BACtC,OAAO,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;yBAC1F;wBACD,OAAO,UAAU,CAAC;qBACrB;oBACD,KAAK,SAAS,CAAC,0BAA0B,CAAC,CAAC;wBACvC,OAAO,UAAU,CAAC;qBACrB;iBACJ;gBACD,MAAM;aACT;SACJ;QAED,OAAO,CAAC,CAAC;IACb,CAAC;IAED;;;;;;;OAOG;IACI,yBAAyB,CAAC,UAAkB,EAAE,QAAgB,EAAE,QAAgB,EAAE,MAAe;QACpG,IAAI,SAAS,CAAC,oCAAoC,EAAE;YAChD,IAAI,MAAM,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAClE,OAAO,MAAM,CAAC;aACjB;YACD,OAAO,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC/D;QAED,IAAI,MAAM,EAAE;YACR,MAAM,CAAC,SAAS,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YACzD,OAAO,MAAM,CAAC;SACjB;QACD,OAAO,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACvD,CAAC;IAED;;;OAGG;IACI,KAAK;QACR,MAAM,KAAK,GAAG,IAAI,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;QAE7H,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QAC3C,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEzC,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,KAAK,CAAC,OAAO,GAAG,EAAE,CAAC;YACnB,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC7B,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACjC,IAAI,CAAC,KAAK,EAAE;oBACR,SAAS;iBACZ;gBACD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;aACvC;SACJ;QAED,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,MAA4B;QACvC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACjC,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,MAAM,mBAAmB,GAAQ,EAAE,CAAC;QAEpC,mBAAmB,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrC,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC;QACnD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC7C,mBAAmB,CAAC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;QACjD,mBAAmB,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;QACzD,mBAAmB,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAEvD,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,mBAAmB,CAAC,IAAI,GAAG,EAAE,CAAC;QAC9B,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC5B,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YAEjC,MAAM,GAAG,GAAQ,EAAE,CAAC;YACpB,GAAG,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;YAE/B,QAAQ,QAAQ,EAAE;gBACd,KAAK,SAAS,CAAC,mBAAmB;oBAC9B,GAAG,CAAC,MAAM,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBAClC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;wBACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;qBAC3C;oBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;wBACvC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;4BACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;qBAC5C;oBACD,IAAI,YAAY,CAAC,aAAa,KAAK,SAAS,EAAE;wBAC1C,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;4BACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;qBAC/C;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB,CAAC;gBACxC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC,KAAK,SAAS,CAAC,oBAAoB,CAAC;gBACpC,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,GAAG,CAAC,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;oBAC1C,IAAI,YAAY,CAAC,SAAS,IAAI,SAAS,EAAE;wBACrC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CAAC;qBACrD;oBACD,IAAI,YAAY,CAAC,UAAU,IAAI,SAAS,EAAE;wBACtC,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;4BACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC;qBACtD;oBACD,IAAI,YAAY,CAAC,aAAa,KAAK,SAAS,EAAE;wBAC1C,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE;4BACtC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;4BACvC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;yBAC9B;wBACD,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;qBAC/C;oBACD,MAAM;aACb;YAED,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SACtC;QAED,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAChC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;YAC7B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAElC,IAAI,CAAC,MAAM,EAAE;gBACT,SAAS;aACZ;YACD,MAAM,KAAK,GAAQ,EAAE,CAAC;YACtB,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;YAClB,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC;YACzB,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;YACrB,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC1C;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;IAgDD;;OAEG;IACI,MAAM,CAAC,cAAc,CAAC,IAAS,EAAE,KAAU,EAAE,MAAc;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,CAAC,IAAI,EAAE;YAClB,iBAAiB;YACjB,OAAO,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SAChD;aAAM,IAAI,WAAW,CAAC,KAAK,EAAE;YAC1B,kBAAkB;YAClB,OAAO,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;SACjD;aAAM,IAAI,IAAI,CAAC,OAAO,EAAE;YACrB,SAAS;YACT,OAAO,IAAI,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,MAAM,GAAG,KAAK,CAAC;SACjD;aAAM;YACH,yBAAyB;YACzB,OAAO,KAAK,CAAC;SAChB;IACL,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,KAAK,CAAC,eAAoB;QACpC,MAAM,SAAS,GAAG,IAAI,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,cAAc,EAAE,eAAe,CAAC,QAAQ,EAAE,eAAe,CAAC,YAAY,CAAC,CAAC;QAExK,MAAM,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QAC1C,MAAM,IAAI,GAAyB,EAAE,CAAC;QACtC,IAAI,IAAI,CAAC;QACT,IAAI,KAAa,CAAC;QAElB,IAAI,eAAe,CAAC,cAAc,EAAE;YAChC,SAAS,CAAC,cAAc,GAAG,eAAe,CAAC,cAAc,CAAC;SAC7D;QAED,IAAI,eAAe,CAAC,aAAa,EAAE;YAC/B,SAAS,CAAC,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC;SAC3D;QAED,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YAC1D,MAAM,GAAG,GAAG,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACxC,IAAI,SAAS,GAAQ,SAAS,CAAC;YAC/B,IAAI,UAAU,GAAQ,SAAS,CAAC;YAChC,IAAI,aAAa,GAAQ,SAAS,CAAC;YAEnC,QAAQ,QAAQ,EAAE;gBACd,KAAK,SAAS,CAAC,mBAAmB;oBAC9B,IAAI,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;oBACrB,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;wBACxB,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;qBAC7B;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;wBACxB,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;qBAC9B;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;wBACxB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;qBACjC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,wBAAwB;oBACnC,IAAI,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACxC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;wBACxB,MAAM,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBAChE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE;4BACvC,SAAS,GAAG,UAAU,CAAC;yBAC1B;qBACJ;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE;wBACzB,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;wBAClE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE;4BACxC,UAAU,GAAG,WAAW,CAAC;yBAC5B;qBACJ;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE;wBACzB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,MAAM,IAAI,EAAE,EAAE;wBACzB,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;qBAClC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC/C;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChD;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;qBACjC;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACpC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBAC/C;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChD;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBACnD;oBACD,MAAM;gBACV,KAAK,SAAS,CAAC,qBAAqB,CAAC;gBACrC;oBACI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;oBACrC,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBAChD;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,UAAU,GAAG,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;qBACjD;oBACD,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE;wBACf,aAAa,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;qBACjC;oBACD,MAAM;aACb;YAED,MAAM,OAAO,GAAQ,EAAE,CAAC;YACxB,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;YAC1B,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YAErB,IAAI,SAAS,IAAI,SAAS,EAAE;gBACxB,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;aACjC;YACD,IAAI,UAAU,IAAI,SAAS,EAAE;gBACzB,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;aACnC;YACD,IAAI,aAAa,IAAI,SAAS,EAAE;gBAC5B,OAAO,CAAC,aAAa,GAAG,aAAa,CAAC;aACzC;YACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACtB;QAED,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAExB,IAAI,eAAe,CAAC,MAAM,EAAE;YACxB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,eAAe,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC5D,IAAI,GAAG,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;gBACrC,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;aACxD;SACJ;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,0BAA0B,CAAC,MAAmB,EAAE,WAAgB;QAC1E,mBAAmB,CAAC,0BAA0B,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IACxE,CAAC;IAED;;;;;OAKG;IACI,MAAM,CAAC,kBAAkB,CAAC,IAAsB,EAAE,GAAW;QAChE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,IAAI,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBAC3D,IAAI,mBAAmB,CAAC,UAAU,EAAE;4BAChC,mBAAmB,GAAG,mBAAmB,CAAC,UAAU,CAAC;yBACxD;wBAED,IAAI,mBAAmB,CAAC,MAAM,EAAE;4BAC5B,MAAM,MAAM,GAAG,IAAI,KAAK,EAAa,CAAC;4BACtC,KAAK,MAAM,mBAAmB,IAAI,mBAAmB,EAAE;gCACnD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,CAAC;6BAChD;4BAED,OAAO,CAAC,MAAM,CAAC,CAAC;yBACnB;6BAAM;4BACH,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;4BAE/C,IAAI,IAAI,EAAE;gCACN,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;6BACtB;4BAED,OAAO,CAAC,MAAM,CAAC,CAAC;yBACnB;qBACJ;yBAAM;wBACH,MAAM,CAAC,8BAA8B,CAAC,CAAC;qBAC1C;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACzB,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,MAAM,CAAC,qBAAqB,CAAC,SAAiB;QACjD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACnC,MAAM,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,GAAG,EAAE;gBAC9C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,EAAE;oBACzB,IAAI,OAAO,CAAC,MAAM,IAAI,GAAG,EAAE;wBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC;wBAEzE,IAAI,OAAO,CAAC,UAAU,EAAE;4BACpB,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;4BAC3D,MAAM,OAAO,GAAG,IAAI,KAAK,EAAa,CAAC;4BACvC,KAAK,MAAM,mBAAmB,IAAI,mBAAmB,CAAC,UAAU,EAAE;gCAC9D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;gCAC/C,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;gCAC7B,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;6BACxB;4BAED,OAAO,CAAC,OAAO,CAAC,CAAC;yBACpB;6BAAM;4BACH,MAAM,mBAAmB,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;4BAC1D,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;4BAE/C,MAAM,CAAC,SAAS,GAAG,SAAS,CAAC;4BAE7B,OAAO,CAAC,MAAM,CAAC,CAAC;yBACnB;qBACJ;yBAAM;wBACH,MAAM,CAAC,6BAA6B,GAAG,SAAS,CAAC,CAAC;qBACrD;iBACJ;YACL,CAAC,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1E,OAAO,CAAC,IAAI,EAAE,CAAC;QACnB,CAAC,CAAC,CAAC;IACP,CAAC;;AA38Cc,4BAAkB,GAAG,CAAC,CAAC;AAEtC;;GAEG;AACW,oCAA0B,GAAG,KAAK,CAAC;AAEjD;;GAEG;AACW,8CAAoC,GAAG,IAAI,CAAC;AAO1D,sCAAsC;AACxB,oBAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAupChD,UAAU;AACV;;GAEG;AACoB,6BAAmB,GAAG,CAAC,CAAC;AAC/C;;GAEG;AACoB,+BAAqB,GAAG,CAAC,CAAC;AACjD;;GAEG;AACoB,kCAAwB,GAAG,CAAC,CAAC;AACpD;;GAEG;AACoB,8BAAoB,GAAG,CAAC,CAAC;AAChD;;GAEG;AACoB,8BAAoB,GAAG,CAAC,CAAC;AAChD;;GAEG;AACoB,8BAAoB,GAAG,CAAC,CAAC;AAChD;;GAEG;AACoB,+BAAqB,GAAG,CAAC,CAAC;AACjD;;GAEG;AACoB,4BAAkB,GAAG,CAAC,CAAC;AAC9C;;GAEG;AACoB,oCAA0B,GAAG,CAAC,CAAC;AACtD;;GAEG;AACoB,iCAAuB,GAAG,CAAC,CAAC;AACnD;;GAEG;AACoB,oCAA0B,GAAG,CAAC,CAAC;AAwPtD;;;;;GAKG;AACW,gCAAsB,GAAG,SAAS,CAAC,qBAAqB,CAAC;AAG3E,aAAa,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;AAC9C,IAAI,CAAC,sBAAsB,GAAG,CAAC,IAAY,EAAE,IAAY,EAAE,EAAU,EAAE,EAAE,CAAC,IAAI,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC", "sourcesContent": ["import type { IEasingFunction, EasingFunction } from \"./easing\";\r\nimport { Vector3, Quaternion, Vector2, Matrix, TmpVectors } from \"../Maths/math.vector\";\r\nimport { Color3, Color4 } from \"../Maths/math.color\";\r\nimport { <PERSON><PERSON><PERSON> } from \"../Maths/math.scalar\";\r\n\r\nimport type { Nullable } from \"../types\";\r\nimport type { Scene } from \"../scene\";\r\nimport { SerializationHelper } from \"../Misc/decorators\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\nimport { AnimationKeyInterpolation } from \"./animationKey\";\r\nimport { AnimationRange } from \"./animationRange\";\r\nimport type { AnimationEvent } from \"./animationEvent\";\r\nimport { Node } from \"../node\";\r\nimport type { IAnimatable } from \"./animatable.interface\";\r\nimport { Size } from \"../Maths/math.size\";\r\nimport { WebRequest } from \"../Misc/webRequest\";\r\nimport { Constants } from \"../Engines/constants\";\r\n\r\ndeclare type Animatable = import(\"./animatable\").Animatable;\r\ndeclare type RuntimeAnimation = import(\"./runtimeAnimation\").RuntimeAnimation;\r\n\r\n/**\r\n * @internal\r\n */\r\n// eslint-disable-next-line @typescript-eslint/naming-convention\r\nexport class _IAnimationState {\r\n    key: number;\r\n    repeatCount: number;\r\n    workValue?: any;\r\n    loopMode?: number;\r\n    offsetValue?: any;\r\n    highLimitValue?: any;\r\n}\r\n\r\n/**\r\n * Class used to store any kind of animation\r\n */\r\nexport class Animation {\r\n    private static _UniqueIdGenerator = 0;\r\n\r\n    /**\r\n     * Use matrix interpolation instead of using direct key value when animating matrices\r\n     */\r\n    public static AllowMatricesInterpolation = false;\r\n\r\n    /**\r\n     * When matrix interpolation is enabled, this boolean forces the system to use Matrix.DecomposeLerp instead of Matrix.Lerp. Interpolation is more precise but slower\r\n     */\r\n    public static AllowMatrixDecomposeForInterpolation = true;\r\n\r\n    /**\r\n     * Gets or sets the unique id of the animation (the uniqueness is solely among other animations)\r\n     */\r\n    public uniqueId: number;\r\n\r\n    /** Define the Url to load snippets */\r\n    public static SnippetUrl = Constants.SnippetUrl;\r\n\r\n    /** Snippet ID if the animation was created from the snippet server */\r\n    public snippetId: string;\r\n\r\n    /**\r\n     * Stores the key frames of the animation\r\n     */\r\n    private _keys: Array<IAnimationKey>;\r\n\r\n    /**\r\n     * Stores the easing function of the animation\r\n     */\r\n    private _easingFunction: Nullable<IEasingFunction> = null;\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _runtimeAnimations = new Array<RuntimeAnimation>();\r\n\r\n    /**\r\n     * The set of event that will be linked to this animation\r\n     */\r\n    private _events = new Array<AnimationEvent>();\r\n\r\n    /**\r\n     * Stores an array of target property paths\r\n     */\r\n    public targetPropertyPath: string[];\r\n\r\n    /**\r\n     * Stores the blending speed of the animation\r\n     */\r\n    public blendingSpeed = 0.01;\r\n\r\n    /**\r\n     * Stores the animation ranges for the animation\r\n     */\r\n    private _ranges: { [name: string]: Nullable<AnimationRange> } = {};\r\n\r\n    /**\r\n     * @internal Internal use\r\n     */\r\n    public static _PrepareAnimation(\r\n        name: string,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction\r\n    ): Nullable<Animation> {\r\n        let dataType = undefined;\r\n\r\n        if (!isNaN(parseFloat(from)) && isFinite(from)) {\r\n            dataType = Animation.ANIMATIONTYPE_FLOAT;\r\n        } else if (from instanceof Quaternion) {\r\n            dataType = Animation.ANIMATIONTYPE_QUATERNION;\r\n        } else if (from instanceof Vector3) {\r\n            dataType = Animation.ANIMATIONTYPE_VECTOR3;\r\n        } else if (from instanceof Vector2) {\r\n            dataType = Animation.ANIMATIONTYPE_VECTOR2;\r\n        } else if (from instanceof Color3) {\r\n            dataType = Animation.ANIMATIONTYPE_COLOR3;\r\n        } else if (from instanceof Color4) {\r\n            dataType = Animation.ANIMATIONTYPE_COLOR4;\r\n        } else if (from instanceof Size) {\r\n            dataType = Animation.ANIMATIONTYPE_SIZE;\r\n        }\r\n\r\n        if (dataType == undefined) {\r\n            return null;\r\n        }\r\n\r\n        const animation = new Animation(name, targetProperty, framePerSecond, dataType, loopMode);\r\n\r\n        const keys: Array<IAnimationKey> = [\r\n            { frame: 0, value: from },\r\n            { frame: totalFrame, value: to },\r\n        ];\r\n        animation.setKeys(keys);\r\n\r\n        if (easingFunction !== undefined) {\r\n            animation.setEasingFunction(easingFunction);\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Sets up an animation\r\n     * @param property The property to animate\r\n     * @param animationType The animation type to apply\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param easingFunction The easing function used in the animation\r\n     * @returns The created animation\r\n     */\r\n    public static CreateAnimation(property: string, animationType: number, framePerSecond: number, easingFunction: EasingFunction): Animation {\r\n        const animation: Animation = new Animation(property + \"Animation\", property, framePerSecond, animationType, Animation.ANIMATIONLOOPMODE_CONSTANT);\r\n\r\n        animation.setEasingFunction(easingFunction);\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Create and start an animation on a node\r\n     * @param name defines the name of the global animation that will be run on all nodes\r\n     * @param target defines the target where the animation will take place\r\n     * @param targetProperty defines property to animate\r\n     * @param framePerSecond defines the number of frame per second yo use\r\n     * @param totalFrame defines the number of frames in total\r\n     * @param from defines the initial value\r\n     * @param to defines the final value\r\n     * @param loopMode defines which loop mode you want to use (off by default)\r\n     * @param easingFunction defines the easing function to use (linear by default)\r\n     * @param onAnimationEnd defines the callback to call when animation end\r\n     * @param scene defines the hosting scene\r\n     * @returns the animatable created for this animation\r\n     */\r\n    public static CreateAndStartAnimation(\r\n        name: string,\r\n        target: any,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void,\r\n        scene?: Scene\r\n    ): Nullable<Animatable> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        if (target.getScene) {\r\n            scene = target.getScene();\r\n        }\r\n\r\n        if (!scene) {\r\n            return null;\r\n        }\r\n\r\n        return scene.beginDirectAnimation(target, [animation], 0, totalFrame, animation.loopMode === 1, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Create and start an animation on a node and its descendants\r\n     * @param name defines the name of the global animation that will be run on all nodes\r\n     * @param node defines the root node where the animation will take place\r\n     * @param directDescendantsOnly if true only direct descendants will be used, if false direct and also indirect (children of children, an so on in a recursive manner) descendants will be used\r\n     * @param targetProperty defines property to animate\r\n     * @param framePerSecond defines the number of frame per second to use\r\n     * @param totalFrame defines the number of frames in total\r\n     * @param from defines the initial value\r\n     * @param to defines the final value\r\n     * @param loopMode defines which loop mode you want to use (off by default)\r\n     * @param easingFunction defines the easing function to use (linear by default)\r\n     * @param onAnimationEnd defines the callback to call when an animation ends (will be called once per node)\r\n     * @returns the list of animatables created for all nodes\r\n     * @example https://www.babylonjs-playground.com/#MH0VLI\r\n     */\r\n    public static CreateAndStartHierarchyAnimation(\r\n        name: string,\r\n        node: Node,\r\n        directDescendantsOnly: boolean,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void\r\n    ): Nullable<Animatable[]> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        const scene = node.getScene();\r\n        return scene.beginDirectHierarchyAnimation(node, directDescendantsOnly, [animation], 0, totalFrame, animation.loopMode === 1, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation, merges it with the existing animations and starts it\r\n     * @param name Name of the animation\r\n     * @param node Node which contains the scene that begins the animations\r\n     * @param targetProperty Specifies which property to animate\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param totalFrame The total number of frames\r\n     * @param from The frame at the beginning of the animation\r\n     * @param to The frame at the end of the animation\r\n     * @param loopMode Specifies the loop mode of the animation\r\n     * @param easingFunction (Optional) The easing function of the animation, which allow custom mathematical formulas for animations\r\n     * @param onAnimationEnd Callback to run once the animation is complete\r\n     * @returns Nullable animation\r\n     */\r\n    public static CreateMergeAndStartAnimation(\r\n        name: string,\r\n        node: Node,\r\n        targetProperty: string,\r\n        framePerSecond: number,\r\n        totalFrame: number,\r\n        from: any,\r\n        to: any,\r\n        loopMode?: number,\r\n        easingFunction?: EasingFunction,\r\n        onAnimationEnd?: () => void\r\n    ): Nullable<Animatable> {\r\n        const animation = Animation._PrepareAnimation(name, targetProperty, framePerSecond, totalFrame, from, to, loopMode, easingFunction);\r\n\r\n        if (!animation) {\r\n            return null;\r\n        }\r\n\r\n        node.animations.push(animation);\r\n\r\n        return node.getScene().beginAnimation(node, 0, totalFrame, animation.loopMode === 1, 1.0, onAnimationEnd);\r\n    }\r\n\r\n    /**\r\n     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.\r\n     * @param sourceAnimation defines the Animation containing keyframes to convert\r\n     * @param referenceFrame defines the frame that keyframes in the range will be relative to\r\n     * @param range defines the name of the AnimationRange belonging to the Animation to convert\r\n     * @param cloneOriginal defines whether or not to clone the animation and convert the clone or convert the original animation (default is false)\r\n     * @param clonedName defines the name of the resulting cloned Animation if cloneOriginal is true\r\n     * @returns a new Animation if cloneOriginal is true or the original Animation if cloneOriginal is false\r\n     */\r\n    public static MakeAnimationAdditive(sourceAnimation: Animation, referenceFrame = 0, range?: string, cloneOriginal = false, clonedName?: string): Animation {\r\n        let animation = sourceAnimation;\r\n\r\n        if (cloneOriginal) {\r\n            animation = sourceAnimation.clone();\r\n            animation.name = clonedName || animation.name;\r\n        }\r\n\r\n        if (!animation._keys.length) {\r\n            return animation;\r\n        }\r\n\r\n        referenceFrame = referenceFrame >= 0 ? referenceFrame : 0;\r\n        let startIndex = 0;\r\n        const firstKey = animation._keys[0];\r\n        let endIndex = animation._keys.length - 1;\r\n        const lastKey = animation._keys[endIndex];\r\n        const valueStore = {\r\n            referenceValue: firstKey.value,\r\n            referencePosition: TmpVectors.Vector3[0],\r\n            referenceQuaternion: TmpVectors.Quaternion[0],\r\n            referenceScaling: TmpVectors.Vector3[1],\r\n            keyPosition: TmpVectors.Vector3[2],\r\n            keyQuaternion: TmpVectors.Quaternion[1],\r\n            keyScaling: TmpVectors.Vector3[3],\r\n        };\r\n        let referenceFound = false;\r\n        let from = firstKey.frame;\r\n        let to = lastKey.frame;\r\n        if (range) {\r\n            const rangeValue = animation.getRange(range);\r\n\r\n            if (rangeValue) {\r\n                from = rangeValue.from;\r\n                to = rangeValue.to;\r\n            }\r\n        }\r\n        let fromKeyFound = firstKey.frame === from;\r\n        let toKeyFound = lastKey.frame === to;\r\n\r\n        // There's only one key, so use it\r\n        if (animation._keys.length === 1) {\r\n            const value = animation._getKeyValue(animation._keys[0]);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n            referenceFound = true;\r\n        }\r\n\r\n        // Reference frame is before the first frame, so just use the first frame\r\n        else if (referenceFrame <= firstKey.frame) {\r\n            const value = animation._getKeyValue(firstKey.value);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n            referenceFound = true;\r\n        }\r\n\r\n        // Reference frame is after the last frame, so just use the last frame\r\n        else if (referenceFrame >= lastKey.frame) {\r\n            const value = animation._getKeyValue(lastKey.value);\r\n            valueStore.referenceValue = value.clone ? value.clone() : value;\r\n            referenceFound = true;\r\n        }\r\n\r\n        // Find key bookends, create them if they don't exist\r\n        let index = 0;\r\n        while (!referenceFound || !fromKeyFound || (!toKeyFound && index < animation._keys.length - 1)) {\r\n            const currentKey = animation._keys[index];\r\n            const nextKey = animation._keys[index + 1];\r\n\r\n            // If reference frame wasn't found yet, check if we can interpolate to it\r\n            if (!referenceFound && referenceFrame >= currentKey.frame && referenceFrame <= nextKey.frame) {\r\n                let value;\r\n\r\n                if (referenceFrame === currentKey.frame) {\r\n                    value = animation._getKeyValue(currentKey.value);\r\n                } else if (referenceFrame === nextKey.frame) {\r\n                    value = animation._getKeyValue(nextKey.value);\r\n                } else {\r\n                    const animationState = {\r\n                        key: index,\r\n                        repeatCount: 0,\r\n                        loopMode: this.ANIMATIONLOOPMODE_CONSTANT,\r\n                    };\r\n                    value = animation._interpolate(referenceFrame, animationState);\r\n                }\r\n\r\n                valueStore.referenceValue = value.clone ? value.clone() : value;\r\n                referenceFound = true;\r\n            }\r\n\r\n            // If from key wasn't found yet, check if we can interpolate to it\r\n            if (!fromKeyFound && from >= currentKey.frame && from <= nextKey.frame) {\r\n                if (from === currentKey.frame) {\r\n                    startIndex = index;\r\n                } else if (from === nextKey.frame) {\r\n                    startIndex = index + 1;\r\n                } else {\r\n                    const animationState = {\r\n                        key: index,\r\n                        repeatCount: 0,\r\n                        loopMode: this.ANIMATIONLOOPMODE_CONSTANT,\r\n                    };\r\n                    const value = animation._interpolate(from, animationState);\r\n                    const key: IAnimationKey = {\r\n                        frame: from,\r\n                        value: value.clone ? value.clone() : value,\r\n                    };\r\n                    animation._keys.splice(index + 1, 0, key);\r\n                    startIndex = index + 1;\r\n                }\r\n\r\n                fromKeyFound = true;\r\n            }\r\n\r\n            // If to key wasn't found yet, check if we can interpolate to it\r\n            if (!toKeyFound && to >= currentKey.frame && to <= nextKey.frame) {\r\n                if (to === currentKey.frame) {\r\n                    endIndex = index;\r\n                } else if (to === nextKey.frame) {\r\n                    endIndex = index + 1;\r\n                } else {\r\n                    const animationState = {\r\n                        key: index,\r\n                        repeatCount: 0,\r\n                        loopMode: this.ANIMATIONLOOPMODE_CONSTANT,\r\n                    };\r\n                    const value = animation._interpolate(to, animationState);\r\n                    const key: IAnimationKey = {\r\n                        frame: to,\r\n                        value: value.clone ? value.clone() : value,\r\n                    };\r\n                    animation._keys.splice(index + 1, 0, key);\r\n                    endIndex = index + 1;\r\n                }\r\n\r\n                toKeyFound = true;\r\n            }\r\n\r\n            index++;\r\n        }\r\n\r\n        // Conjugate the quaternion\r\n        if (animation.dataType === Animation.ANIMATIONTYPE_QUATERNION) {\r\n            valueStore.referenceValue.normalize().conjugateInPlace();\r\n        }\r\n\r\n        // Decompose matrix and conjugate the quaternion\r\n        else if (animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\r\n            valueStore.referenceValue.decompose(valueStore.referenceScaling, valueStore.referenceQuaternion, valueStore.referencePosition);\r\n            valueStore.referenceQuaternion.normalize().conjugateInPlace();\r\n        }\r\n\r\n        // Subtract the reference value from all of the key values\r\n        for (index = startIndex; index <= endIndex; index++) {\r\n            const key = animation._keys[index];\r\n\r\n            // If this key was duplicated to create a frame 0 key, skip it because its value has already been updated\r\n            if (index && animation.dataType !== Animation.ANIMATIONTYPE_FLOAT && key.value === firstKey.value) {\r\n                continue;\r\n            }\r\n\r\n            switch (animation.dataType) {\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                    key.value.decompose(valueStore.keyScaling, valueStore.keyQuaternion, valueStore.keyPosition);\r\n                    valueStore.keyPosition.subtractInPlace(valueStore.referencePosition);\r\n                    valueStore.keyScaling.divideInPlace(valueStore.referenceScaling);\r\n                    valueStore.referenceQuaternion.multiplyToRef(valueStore.keyQuaternion, valueStore.keyQuaternion);\r\n                    Matrix.ComposeToRef(valueStore.keyScaling, valueStore.keyQuaternion, valueStore.keyPosition, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                    valueStore.referenceValue.multiplyToRef(key.value, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_VECTOR2:\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    key.value.subtractToRef(valueStore.referenceValue, key.value);\r\n                    break;\r\n\r\n                case Animation.ANIMATIONTYPE_SIZE:\r\n                    key.value.width -= valueStore.referenceValue.width;\r\n                    key.value.height -= valueStore.referenceValue.height;\r\n                    break;\r\n\r\n                default:\r\n                    key.value -= valueStore.referenceValue;\r\n            }\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Transition property of an host to the target Value\r\n     * @param property The property to transition\r\n     * @param targetValue The target Value of the property\r\n     * @param host The object where the property to animate belongs\r\n     * @param scene Scene used to run the animation\r\n     * @param frameRate Framerate (in frame/s) to use\r\n     * @param transition The transition type we want to use\r\n     * @param duration The duration of the animation, in milliseconds\r\n     * @param onAnimationEnd Callback trigger at the end of the animation\r\n     * @returns Nullable animation\r\n     */\r\n    public static TransitionTo(\r\n        property: string,\r\n        targetValue: any,\r\n        host: any,\r\n        scene: Scene,\r\n        frameRate: number,\r\n        transition: Animation,\r\n        duration: number,\r\n        onAnimationEnd: Nullable<() => void> = null\r\n    ): Nullable<Animatable> {\r\n        if (duration <= 0) {\r\n            host[property] = targetValue;\r\n            if (onAnimationEnd) {\r\n                onAnimationEnd();\r\n            }\r\n            return null;\r\n        }\r\n\r\n        const endFrame: number = frameRate * (duration / 1000);\r\n\r\n        transition.setKeys([\r\n            {\r\n                frame: 0,\r\n                value: host[property].clone ? host[property].clone() : host[property],\r\n            },\r\n            {\r\n                frame: endFrame,\r\n                value: targetValue,\r\n            },\r\n        ]);\r\n\r\n        if (!host.animations) {\r\n            host.animations = [];\r\n        }\r\n\r\n        host.animations.push(transition);\r\n\r\n        const animation: Animatable = scene.beginAnimation(host, 0, endFrame, false);\r\n        animation.onAnimationEnd = onAnimationEnd;\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Return the array of runtime animations currently using this animation\r\n     */\r\n    public get runtimeAnimations(): RuntimeAnimation[] {\r\n        return this._runtimeAnimations;\r\n    }\r\n\r\n    /**\r\n     * Specifies if any of the runtime animations are currently running\r\n     */\r\n    public get hasRunningRuntimeAnimations(): boolean {\r\n        for (const runtimeAnimation of this._runtimeAnimations) {\r\n            if (!runtimeAnimation.isStopped()) {\r\n                return true;\r\n            }\r\n        }\r\n\r\n        return false;\r\n    }\r\n\r\n    /**\r\n     * Initializes the animation\r\n     * @param name Name of the animation\r\n     * @param targetProperty Property to animate\r\n     * @param framePerSecond The frames per second of the animation\r\n     * @param dataType The data type of the animation\r\n     * @param loopMode The loop mode of the animation\r\n     * @param enableBlending Specifies if blending should be enabled\r\n     */\r\n    constructor(\r\n        /**Name of the animation */\r\n        public name: string,\r\n        /**Property to animate */\r\n        public targetProperty: string,\r\n        /**The frames per second of the animation */\r\n        public framePerSecond: number,\r\n        /**The data type of the animation */\r\n        public dataType: number,\r\n        /**The loop mode of the animation */\r\n        public loopMode?: number,\r\n        /**Specifies if blending should be enabled */\r\n        public enableBlending?: boolean\r\n    ) {\r\n        this.targetPropertyPath = targetProperty.split(\".\");\r\n        this.dataType = dataType;\r\n        this.loopMode = loopMode === undefined ? Animation.ANIMATIONLOOPMODE_CYCLE : loopMode;\r\n        this.uniqueId = Animation._UniqueIdGenerator++;\r\n    }\r\n\r\n    // Methods\r\n    /**\r\n     * Converts the animation to a string\r\n     * @param fullDetails support for multiple levels of logging within scene loading\r\n     * @returns String form of the animation\r\n     */\r\n    public toString(fullDetails?: boolean): string {\r\n        let ret = \"Name: \" + this.name + \", property: \" + this.targetProperty;\r\n        ret += \", datatype: \" + [\"Float\", \"Vector3\", \"Quaternion\", \"Matrix\", \"Color3\", \"Vector2\"][this.dataType];\r\n        ret += \", nKeys: \" + (this._keys ? this._keys.length : \"none\");\r\n        ret += \", nRanges: \" + (this._ranges ? Object.keys(this._ranges).length : \"none\");\r\n        if (fullDetails) {\r\n            ret += \", Ranges: {\";\r\n            let first = true;\r\n            for (const name in this._ranges) {\r\n                if (first) {\r\n                    ret += \", \";\r\n                    first = false;\r\n                }\r\n                ret += name;\r\n            }\r\n            ret += \"}\";\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Add an event to this animation\r\n     * @param event Event to add\r\n     */\r\n    public addEvent(event: AnimationEvent): void {\r\n        this._events.push(event);\r\n        this._events.sort((a, b) => a.frame - b.frame);\r\n    }\r\n\r\n    /**\r\n     * Remove all events found at the given frame\r\n     * @param frame The frame to remove events from\r\n     */\r\n    public removeEvents(frame: number): void {\r\n        for (let index = 0; index < this._events.length; index++) {\r\n            if (this._events[index].frame === frame) {\r\n                this._events.splice(index, 1);\r\n                index--;\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Retrieves all the events from the animation\r\n     * @returns Events from the animation\r\n     */\r\n    public getEvents(): AnimationEvent[] {\r\n        return this._events;\r\n    }\r\n\r\n    /**\r\n     * Creates an animation range\r\n     * @param name Name of the animation range\r\n     * @param from Starting frame of the animation range\r\n     * @param to Ending frame of the animation\r\n     */\r\n    public createRange(name: string, from: number, to: number): void {\r\n        // check name not already in use; could happen for bones after serialized\r\n        if (!this._ranges[name]) {\r\n            this._ranges[name] = new AnimationRange(name, from, to);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Deletes an animation range by name\r\n     * @param name Name of the animation range to delete\r\n     * @param deleteFrames Specifies if the key frames for the range should also be deleted (true) or not (false)\r\n     */\r\n    public deleteRange(name: string, deleteFrames = true): void {\r\n        const range = this._ranges[name];\r\n        if (!range) {\r\n            return;\r\n        }\r\n        if (deleteFrames) {\r\n            const from = range.from;\r\n            const to = range.to;\r\n\r\n            // this loop MUST go high to low for multiple splices to work\r\n            for (let key = this._keys.length - 1; key >= 0; key--) {\r\n                if (this._keys[key].frame >= from && this._keys[key].frame <= to) {\r\n                    this._keys.splice(key, 1);\r\n                }\r\n            }\r\n        }\r\n        this._ranges[name] = null; // said much faster than 'delete this._range[name]'\r\n    }\r\n\r\n    /**\r\n     * Gets the animation range by name, or null if not defined\r\n     * @param name Name of the animation range\r\n     * @returns Nullable animation range\r\n     */\r\n    public getRange(name: string): Nullable<AnimationRange> {\r\n        return this._ranges[name];\r\n    }\r\n\r\n    /**\r\n     * Gets the key frames from the animation\r\n     * @returns The key frames of the animation\r\n     */\r\n    public getKeys(): Array<IAnimationKey> {\r\n        return this._keys;\r\n    }\r\n\r\n    /**\r\n     * Gets the highest frame rate of the animation\r\n     * @returns Highest frame rate of the animation\r\n     */\r\n    public getHighestFrame(): number {\r\n        let ret = 0;\r\n\r\n        for (let key = 0, nKeys = this._keys.length; key < nKeys; key++) {\r\n            if (ret < this._keys[key].frame) {\r\n                ret = this._keys[key].frame;\r\n            }\r\n        }\r\n        return ret;\r\n    }\r\n\r\n    /**\r\n     * Gets the easing function of the animation\r\n     * @returns Easing function of the animation\r\n     */\r\n    public getEasingFunction(): Nullable<IEasingFunction> {\r\n        return this._easingFunction;\r\n    }\r\n\r\n    /**\r\n     * Sets the easing function of the animation\r\n     * @param easingFunction A custom mathematical formula for animation\r\n     */\r\n    public setEasingFunction(easingFunction: Nullable<IEasingFunction>): void {\r\n        this._easingFunction = easingFunction;\r\n    }\r\n\r\n    /**\r\n     * Interpolates a scalar linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public floatInterpolateFunction(startValue: number, endValue: number, gradient: number): number {\r\n        return Scalar.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a scalar cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public floatInterpolateFunctionWithTangents(startValue: number, outTangent: number, endValue: number, inTangent: number, gradient: number): number {\r\n        return Scalar.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a quaternion using a spherical linear interpolation\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated quaternion value\r\n     */\r\n    public quaternionInterpolateFunction(startValue: Quaternion, endValue: Quaternion, gradient: number): Quaternion {\r\n        return Quaternion.Slerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a quaternion cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated quaternion value\r\n     */\r\n    public quaternionInterpolateFunctionWithTangents(startValue: Quaternion, outTangent: Quaternion, endValue: Quaternion, inTangent: Quaternion, gradient: number): Quaternion {\r\n        return Quaternion.Hermite(startValue, outTangent, endValue, inTangent, gradient).normalize();\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector3 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated scalar value\r\n     */\r\n    public vector3InterpolateFunction(startValue: Vector3, endValue: Vector3, gradient: number): Vector3 {\r\n        return Vector3.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector3 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns InterpolatedVector3 value\r\n     */\r\n    public vector3InterpolateFunctionWithTangents(startValue: Vector3, outTangent: Vector3, endValue: Vector3, inTangent: Vector3, gradient: number): Vector3 {\r\n        return Vector3.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector2 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated Vector2 value\r\n     */\r\n    public vector2InterpolateFunction(startValue: Vector2, endValue: Vector2, gradient: number): Vector2 {\r\n        return Vector2.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Vector2 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate (value between 0 and 1)\r\n     * @returns Interpolated Vector2 value\r\n     */\r\n    public vector2InterpolateFunctionWithTangents(startValue: Vector2, outTangent: Vector2, endValue: Vector2, inTangent: Vector2, gradient: number): Vector2 {\r\n        return Vector2.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a size linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Size value\r\n     */\r\n    public sizeInterpolateFunction(startValue: Size, endValue: Size, gradient: number): Size {\r\n        return Size.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color3 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Color3 value\r\n     */\r\n    public color3InterpolateFunction(startValue: Color3, endValue: Color3, gradient: number): Color3 {\r\n        return Color3.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color3 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns interpolated value\r\n     */\r\n    public color3InterpolateFunctionWithTangents(startValue: Color3, outTangent: Color3, endValue: Color3, inTangent: Color3, gradient: number): Color3 {\r\n        return Color3.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color4 linearly\r\n     * @param startValue Start value of the animation curve\r\n     * @param endValue End value of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns Interpolated Color3 value\r\n     */\r\n    public color4InterpolateFunction(startValue: Color4, endValue: Color4, gradient: number): Color4 {\r\n        return Color4.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Interpolates a Color4 cubically\r\n     * @param startValue Start value of the animation curve\r\n     * @param outTangent End tangent of the animation\r\n     * @param endValue End value of the animation curve\r\n     * @param inTangent Start tangent of the animation curve\r\n     * @param gradient Scalar amount to interpolate\r\n     * @returns interpolated value\r\n     */\r\n    public color4InterpolateFunctionWithTangents(startValue: Color4, outTangent: Color4, endValue: Color4, inTangent: Color4, gradient: number): Color4 {\r\n        return Color4.Hermite(startValue, outTangent, endValue, inTangent, gradient);\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _getKeyValue(value: any): any {\r\n        if (typeof value === \"function\") {\r\n            return value();\r\n        }\r\n\r\n        return value;\r\n    }\r\n\r\n    /**\r\n     * Evaluate the animation value at a given frame\r\n     * @param currentFrame defines the frame where we want to evaluate the animation\r\n     * @returns the animation value\r\n     */\r\n    public evaluate(currentFrame: number) {\r\n        return this._interpolate(currentFrame, {\r\n            key: 0,\r\n            repeatCount: 0,\r\n            loopMode: Animation.ANIMATIONLOOPMODE_CONSTANT,\r\n        });\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _interpolate(currentFrame: number, state: _IAnimationState): any {\r\n        if (state.loopMode === Animation.ANIMATIONLOOPMODE_CONSTANT && state.repeatCount > 0) {\r\n            return state.highLimitValue.clone ? state.highLimitValue.clone() : state.highLimitValue;\r\n        }\r\n\r\n        const keys = this._keys;\r\n        const keysLength = keys.length;\r\n\r\n        let key = state.key;\r\n\r\n        while (key >= 0 && currentFrame < keys[key].frame) {\r\n            --key;\r\n        }\r\n\r\n        while (key + 1 <= keysLength - 1 && currentFrame >= keys[key + 1].frame) {\r\n            ++key;\r\n        }\r\n\r\n        state.key = key;\r\n\r\n        if (key < 0) {\r\n            return this._getKeyValue(keys[0].value);\r\n        } else if (key + 1 > keysLength - 1) {\r\n            return this._getKeyValue(keys[keysLength - 1].value);\r\n        }\r\n\r\n        const startKey = keys[key];\r\n        const endKey = keys[key + 1];\r\n        const startValue = this._getKeyValue(startKey.value);\r\n        const endValue = this._getKeyValue(endKey.value);\r\n        if (startKey.interpolation === AnimationKeyInterpolation.STEP) {\r\n            if (endKey.frame > currentFrame) {\r\n                return startValue;\r\n            } else {\r\n                return endValue;\r\n            }\r\n        }\r\n\r\n        const useTangent = startKey.outTangent !== undefined && endKey.inTangent !== undefined;\r\n        const frameDelta = endKey.frame - startKey.frame;\r\n\r\n        // gradient : percent of currentFrame between the frame inf and the frame sup\r\n        let gradient = (currentFrame - startKey.frame) / frameDelta;\r\n\r\n        // check for easingFunction and correction of gradient\r\n        const easingFunction = this.getEasingFunction();\r\n        if (easingFunction !== null) {\r\n            gradient = easingFunction.ease(gradient);\r\n        }\r\n\r\n        switch (this.dataType) {\r\n            // Float\r\n            case Animation.ANIMATIONTYPE_FLOAT: {\r\n                const floatValue = useTangent\r\n                    ? this.floatInterpolateFunctionWithTangents(startValue, startKey.outTangent * frameDelta, endValue, endKey.inTangent * frameDelta, gradient)\r\n                    : this.floatInterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return floatValue;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return state.offsetValue * state.repeatCount + floatValue;\r\n                }\r\n                break;\r\n            }\r\n            // Quaternion\r\n            case Animation.ANIMATIONTYPE_QUATERNION: {\r\n                const quatValue = useTangent\r\n                    ? this.quaternionInterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.quaternionInterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return quatValue;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return quatValue.addInPlace(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n\r\n                return quatValue;\r\n            }\r\n            // Vector3\r\n            case Animation.ANIMATIONTYPE_VECTOR3: {\r\n                const vec3Value = useTangent\r\n                    ? this.vector3InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.vector3InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return vec3Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return vec3Value.add(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Vector2\r\n            case Animation.ANIMATIONTYPE_VECTOR2: {\r\n                const vec2Value = useTangent\r\n                    ? this.vector2InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.vector2InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return vec2Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return vec2Value.add(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Size\r\n            case Animation.ANIMATIONTYPE_SIZE: {\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return this.sizeInterpolateFunction(startValue, endValue, gradient);\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return this.sizeInterpolateFunction(startValue, endValue, gradient).add(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Color3\r\n            case Animation.ANIMATIONTYPE_COLOR3: {\r\n                const color3Value = useTangent\r\n                    ? this.color3InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.color3InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return color3Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return color3Value.add(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Color4\r\n            case Animation.ANIMATIONTYPE_COLOR4: {\r\n                const color4Value = useTangent\r\n                    ? this.color4InterpolateFunctionWithTangents(startValue, startKey.outTangent.scale(frameDelta), endValue, endKey.inTangent.scale(frameDelta), gradient)\r\n                    : this.color4InterpolateFunction(startValue, endValue, gradient);\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT:\r\n                        return color4Value;\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE:\r\n                        return color4Value.add(state.offsetValue.scale(state.repeatCount));\r\n                }\r\n                break;\r\n            }\r\n            // Matrix\r\n            case Animation.ANIMATIONTYPE_MATRIX: {\r\n                switch (state.loopMode) {\r\n                    case Animation.ANIMATIONLOOPMODE_CYCLE:\r\n                    case Animation.ANIMATIONLOOPMODE_CONSTANT: {\r\n                        if (Animation.AllowMatricesInterpolation) {\r\n                            return this.matrixInterpolateFunction(startValue, endValue, gradient, state.workValue);\r\n                        }\r\n                        return startValue;\r\n                    }\r\n                    case Animation.ANIMATIONLOOPMODE_RELATIVE: {\r\n                        return startValue;\r\n                    }\r\n                }\r\n                break;\r\n            }\r\n        }\r\n\r\n        return 0;\r\n    }\r\n\r\n    /**\r\n     * Defines the function to use to interpolate matrices\r\n     * @param startValue defines the start matrix\r\n     * @param endValue defines the end matrix\r\n     * @param gradient defines the gradient between both matrices\r\n     * @param result defines an optional target matrix where to store the interpolation\r\n     * @returns the interpolated matrix\r\n     */\r\n    public matrixInterpolateFunction(startValue: Matrix, endValue: Matrix, gradient: number, result?: Matrix): Matrix {\r\n        if (Animation.AllowMatrixDecomposeForInterpolation) {\r\n            if (result) {\r\n                Matrix.DecomposeLerpToRef(startValue, endValue, gradient, result);\r\n                return result;\r\n            }\r\n            return Matrix.DecomposeLerp(startValue, endValue, gradient);\r\n        }\r\n\r\n        if (result) {\r\n            Matrix.LerpToRef(startValue, endValue, gradient, result);\r\n            return result;\r\n        }\r\n        return Matrix.Lerp(startValue, endValue, gradient);\r\n    }\r\n\r\n    /**\r\n     * Makes a copy of the animation\r\n     * @returns Cloned animation\r\n     */\r\n    public clone(): Animation {\r\n        const clone = new Animation(this.name, this.targetPropertyPath.join(\".\"), this.framePerSecond, this.dataType, this.loopMode);\r\n\r\n        clone.enableBlending = this.enableBlending;\r\n        clone.blendingSpeed = this.blendingSpeed;\r\n\r\n        if (this._keys) {\r\n            clone.setKeys(this._keys);\r\n        }\r\n\r\n        if (this._ranges) {\r\n            clone._ranges = {};\r\n            for (const name in this._ranges) {\r\n                const range = this._ranges[name];\r\n                if (!range) {\r\n                    continue;\r\n                }\r\n                clone._ranges[name] = range.clone();\r\n            }\r\n        }\r\n\r\n        return clone;\r\n    }\r\n\r\n    /**\r\n     * Sets the key frames of the animation\r\n     * @param values The animation key frames to set\r\n     */\r\n    public setKeys(values: Array<IAnimationKey>): void {\r\n        this._keys = values.slice(0);\r\n    }\r\n\r\n    /**\r\n     * Serializes the animation to an object\r\n     * @returns Serialized object\r\n     */\r\n    public serialize(): any {\r\n        const serializationObject: any = {};\r\n\r\n        serializationObject.name = this.name;\r\n        serializationObject.property = this.targetProperty;\r\n        serializationObject.framePerSecond = this.framePerSecond;\r\n        serializationObject.dataType = this.dataType;\r\n        serializationObject.loopBehavior = this.loopMode;\r\n        serializationObject.enableBlending = this.enableBlending;\r\n        serializationObject.blendingSpeed = this.blendingSpeed;\r\n\r\n        const dataType = this.dataType;\r\n        serializationObject.keys = [];\r\n        const keys = this.getKeys();\r\n        for (let index = 0; index < keys.length; index++) {\r\n            const animationKey = keys[index];\r\n\r\n            const key: any = {};\r\n            key.frame = animationKey.frame;\r\n\r\n            switch (dataType) {\r\n                case Animation.ANIMATIONTYPE_FLOAT:\r\n                    key.values = [animationKey.value];\r\n                    if (animationKey.inTangent !== undefined) {\r\n                        key.values.push(animationKey.inTangent);\r\n                    }\r\n                    if (animationKey.outTangent !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.outTangent);\r\n                    }\r\n                    if (animationKey.interpolation !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        if (animationKey.outTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.interpolation);\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    key.values = animationKey.value.asArray();\r\n                    if (animationKey.inTangent != undefined) {\r\n                        key.values.push(animationKey.inTangent.asArray());\r\n                    }\r\n                    if (animationKey.outTangent != undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.outTangent.asArray());\r\n                    }\r\n                    if (animationKey.interpolation !== undefined) {\r\n                        if (animationKey.inTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        if (animationKey.outTangent === undefined) {\r\n                            key.values.push(undefined);\r\n                        }\r\n                        key.values.push(animationKey.interpolation);\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            serializationObject.keys.push(key);\r\n        }\r\n\r\n        serializationObject.ranges = [];\r\n        for (const name in this._ranges) {\r\n            const source = this._ranges[name];\r\n\r\n            if (!source) {\r\n                continue;\r\n            }\r\n            const range: any = {};\r\n            range.name = name;\r\n            range.from = source.from;\r\n            range.to = source.to;\r\n            serializationObject.ranges.push(range);\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n\r\n    // Statics\r\n    /**\r\n     * Float animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_FLOAT = 0;\r\n    /**\r\n     * Vector3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_VECTOR3 = 1;\r\n    /**\r\n     * Quaternion animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_QUATERNION = 2;\r\n    /**\r\n     * Matrix animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_MATRIX = 3;\r\n    /**\r\n     * Color3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_COLOR3 = 4;\r\n    /**\r\n     * Color3 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_COLOR4 = 7;\r\n    /**\r\n     * Vector2 animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_VECTOR2 = 5;\r\n    /**\r\n     * Size animation type\r\n     */\r\n    public static readonly ANIMATIONTYPE_SIZE = 6;\r\n    /**\r\n     * Relative Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_RELATIVE = 0;\r\n    /**\r\n     * Cycle Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_CYCLE = 1;\r\n    /**\r\n     * Constant Loop Mode\r\n     */\r\n    public static readonly ANIMATIONLOOPMODE_CONSTANT = 2;\r\n\r\n    /**\r\n     * @internal\r\n     */\r\n    public static _UniversalLerp(left: any, right: any, amount: number): any {\r\n        const constructor = left.constructor;\r\n        if (constructor.Lerp) {\r\n            // Lerp supported\r\n            return constructor.Lerp(left, right, amount);\r\n        } else if (constructor.Slerp) {\r\n            // Slerp supported\r\n            return constructor.Slerp(left, right, amount);\r\n        } else if (left.toFixed) {\r\n            // Number\r\n            return left * (1.0 - amount) + amount * right;\r\n        } else {\r\n            // Blending not supported\r\n            return right;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Parses an animation object and creates an animation\r\n     * @param parsedAnimation Parsed animation object\r\n     * @returns Animation object\r\n     */\r\n    public static Parse(parsedAnimation: any): Animation {\r\n        const animation = new Animation(parsedAnimation.name, parsedAnimation.property, parsedAnimation.framePerSecond, parsedAnimation.dataType, parsedAnimation.loopBehavior);\r\n\r\n        const dataType = parsedAnimation.dataType;\r\n        const keys: Array<IAnimationKey> = [];\r\n        let data;\r\n        let index: number;\r\n\r\n        if (parsedAnimation.enableBlending) {\r\n            animation.enableBlending = parsedAnimation.enableBlending;\r\n        }\r\n\r\n        if (parsedAnimation.blendingSpeed) {\r\n            animation.blendingSpeed = parsedAnimation.blendingSpeed;\r\n        }\r\n\r\n        for (index = 0; index < parsedAnimation.keys.length; index++) {\r\n            const key = parsedAnimation.keys[index];\r\n            let inTangent: any = undefined;\r\n            let outTangent: any = undefined;\r\n            let interpolation: any = undefined;\r\n\r\n            switch (dataType) {\r\n                case Animation.ANIMATIONTYPE_FLOAT:\r\n                    data = key.values[0];\r\n                    if (key.values.length >= 2) {\r\n                        inTangent = key.values[1];\r\n                    }\r\n                    if (key.values.length >= 3) {\r\n                        outTangent = key.values[2];\r\n                    }\r\n                    if (key.values.length >= 4) {\r\n                        interpolation = key.values[3];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                    data = Quaternion.FromArray(key.values);\r\n                    if (key.values.length >= 8) {\r\n                        const _inTangent = Quaternion.FromArray(key.values.slice(4, 8));\r\n                        if (!_inTangent.equals(Quaternion.Zero())) {\r\n                            inTangent = _inTangent;\r\n                        }\r\n                    }\r\n                    if (key.values.length >= 12) {\r\n                        const _outTangent = Quaternion.FromArray(key.values.slice(8, 12));\r\n                        if (!_outTangent.equals(Quaternion.Zero())) {\r\n                            outTangent = _outTangent;\r\n                        }\r\n                    }\r\n                    if (key.values.length >= 13) {\r\n                        interpolation = key.values[12];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_MATRIX:\r\n                    data = Matrix.FromArray(key.values);\r\n                    if (key.values.length >= 17) {\r\n                        interpolation = key.values[16];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                    data = Color3.FromArray(key.values);\r\n                    if (key.values[3]) {\r\n                        inTangent = Color3.FromArray(key.values[3]);\r\n                    }\r\n                    if (key.values[4]) {\r\n                        outTangent = Color3.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        interpolation = key.values[5];\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_COLOR4:\r\n                    data = Color4.FromArray(key.values);\r\n                    if (key.values[4]) {\r\n                        inTangent = Color4.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        outTangent = Color4.FromArray(key.values[5]);\r\n                    }\r\n                    if (key.values[6]) {\r\n                        interpolation = Color4.FromArray(key.values[6]);\r\n                    }\r\n                    break;\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                default:\r\n                    data = Vector3.FromArray(key.values);\r\n                    if (key.values[3]) {\r\n                        inTangent = Vector3.FromArray(key.values[3]);\r\n                    }\r\n                    if (key.values[4]) {\r\n                        outTangent = Vector3.FromArray(key.values[4]);\r\n                    }\r\n                    if (key.values[5]) {\r\n                        interpolation = key.values[5];\r\n                    }\r\n                    break;\r\n            }\r\n\r\n            const keyData: any = {};\r\n            keyData.frame = key.frame;\r\n            keyData.value = data;\r\n\r\n            if (inTangent != undefined) {\r\n                keyData.inTangent = inTangent;\r\n            }\r\n            if (outTangent != undefined) {\r\n                keyData.outTangent = outTangent;\r\n            }\r\n            if (interpolation != undefined) {\r\n                keyData.interpolation = interpolation;\r\n            }\r\n            keys.push(keyData);\r\n        }\r\n\r\n        animation.setKeys(keys);\r\n\r\n        if (parsedAnimation.ranges) {\r\n            for (index = 0; index < parsedAnimation.ranges.length; index++) {\r\n                data = parsedAnimation.ranges[index];\r\n                animation.createRange(data.name, data.from, data.to);\r\n            }\r\n        }\r\n\r\n        return animation;\r\n    }\r\n\r\n    /**\r\n     * Appends the serialized animations from the source animations\r\n     * @param source Source containing the animations\r\n     * @param destination Target to store the animations\r\n     */\r\n    public static AppendSerializedAnimations(source: IAnimatable, destination: any): void {\r\n        SerializationHelper.AppendSerializedAnimations(source, destination);\r\n    }\r\n\r\n    /**\r\n     * Creates a new animation or an array of animations from a snippet saved in a remote file\r\n     * @param name defines the name of the animation to create (can be null or empty to use the one from the json data)\r\n     * @param url defines the url to load from\r\n     * @returns a promise that will resolve to the new animation or an array of animations\r\n     */\r\n    public static ParseFromFileAsync(name: Nullable<string>, url: string): Promise<Animation | Array<Animation>> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        let serializationObject = JSON.parse(request.responseText);\r\n                        if (serializationObject.animations) {\r\n                            serializationObject = serializationObject.animations;\r\n                        }\r\n\r\n                        if (serializationObject.length) {\r\n                            const output = new Array<Animation>();\r\n                            for (const serializedAnimation of serializationObject) {\r\n                                output.push(this.Parse(serializedAnimation));\r\n                            }\r\n\r\n                            resolve(output);\r\n                        } else {\r\n                            const output = this.Parse(serializationObject);\r\n\r\n                            if (name) {\r\n                                output.name = name;\r\n                            }\r\n\r\n                            resolve(output);\r\n                        }\r\n                    } else {\r\n                        reject(\"Unable to load the animation\");\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", url);\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates an animation or an array of animations from a snippet saved by the Inspector\r\n     * @param snippetId defines the snippet to load\r\n     * @returns a promise that will resolve to the new animation or a new array of animations\r\n     */\r\n    public static ParseFromSnippetAsync(snippetId: string): Promise<Animation | Array<Animation>> {\r\n        return new Promise((resolve, reject) => {\r\n            const request = new WebRequest();\r\n            request.addEventListener(\"readystatechange\", () => {\r\n                if (request.readyState == 4) {\r\n                    if (request.status == 200) {\r\n                        const snippet = JSON.parse(JSON.parse(request.responseText).jsonPayload);\r\n\r\n                        if (snippet.animations) {\r\n                            const serializationObject = JSON.parse(snippet.animations);\r\n                            const outputs = new Array<Animation>();\r\n                            for (const serializedAnimation of serializationObject.animations) {\r\n                                const output = this.Parse(serializedAnimation);\r\n                                output.snippetId = snippetId;\r\n                                outputs.push(output);\r\n                            }\r\n\r\n                            resolve(outputs);\r\n                        } else {\r\n                            const serializationObject = JSON.parse(snippet.animation);\r\n                            const output = this.Parse(serializationObject);\r\n\r\n                            output.snippetId = snippetId;\r\n\r\n                            resolve(output);\r\n                        }\r\n                    } else {\r\n                        reject(\"Unable to load the snippet \" + snippetId);\r\n                    }\r\n                }\r\n            });\r\n\r\n            request.open(\"GET\", this.SnippetUrl + \"/\" + snippetId.replace(/#/g, \"/\"));\r\n            request.send();\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Creates an animation or an array of animations from a snippet saved by the Inspector\r\n     * @deprecated Please use ParseFromSnippetAsync instead\r\n     * @param snippetId defines the snippet to load\r\n     * @returns a promise that will resolve to the new animation or a new array of animations\r\n     */\r\n    public static CreateFromSnippetAsync = Animation.ParseFromSnippetAsync;\r\n}\r\n\r\nRegisterClass(\"BABYLON.Animation\", Animation);\r\nNode._AnimationRangeFactory = (name: string, from: number, to: number) => new AnimationRange(name, from, to);\r\n"]}