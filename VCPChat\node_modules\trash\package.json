{"name": "trash", "version": "9.0.0", "description": "Move files and folders to the trash", "license": "MIT", "repository": "sindresorhus/trash", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && ava --timeout=20m && tsd"}, "files": ["index.js", "index.d.ts", "lib"], "keywords": ["trash", "recycle", "bin", "rm", "rmrf", "<PERSON><PERSON><PERSON>", "remove", "delete", "del", "file", "files", "directory", "directories", "folder", "folders", "xdg"], "dependencies": {"@sindresorhus/chunkify": "^1.0.0", "@stroncium/procfs": "^1.2.1", "globby": "^7.1.1", "is-path-inside": "^4.0.0", "move-file": "^3.1.0", "p-map": "^7.0.2", "xdg-trashdir": "^3.1.0"}, "devDependencies": {"ava": "^6.1.3", "tempfile": "^5.0.0", "tsd": "^0.31.1", "typescript": "^5.5.4", "xo": "^0.59.2"}, "ava": {"workerThreads": false}}