/* Canvasmodules/canvas.css */

/* --- General Body & Layout --- */
body {
    font-family: var(--font-family-sans-serif, sans-serif);
    background-color: transparent; /* Let wallpaper show through */
    color: var(--primary-text);
    margin: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    padding-top: 40px; /* Space for the title bar */
    box-sizing: border-box;
}

/* --- Custom Title Bar (similar to other modules) --- */
#custom-title-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 20px;
    background-color: transparent; /* Title bar is transparent */
    z-index: 1000;
    -webkit-app-region: drag;
    user-select: none;
}

.title-text-container {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    -webkit-app-region: no-drag;
}

#custom-title-bar .title {
    font-size: 14px;
    font-weight: 600;
    color: var(--primary-text);
    opacity: 0.9;
}

#custom-title-bar .window-controls {
    display: flex;
    gap: 10px;
    -webkit-app-region: no-drag;
}

.window-control-btn {
    width: 30px;
    height: 30px;
    border: none;
    background-color: transparent;
    color: var(--secondary-text);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.window-control-btn:hover {
    background-color: var(--button-hover-bg);
    color: var(--primary-text);
}

#close-btn:hover {
    background-color: #e81123;
    color: white;
}

/* --- Main Layout --- */
.main-container {
    display: flex;
    flex-grow: 1;
    overflow: hidden;
    padding: 15px; /* Add padding around the main content area */
    gap: 0; /* Resizer will create the visual gap */
    padding-top: 0; /* Padding is on the container now */
}

/* --- Glass Panel Style (from notes.css) --- */
.glass-panel {
    background-color: var(--panel-bg);
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Important for internal scrolling */
}

/* --- Sidebars --- */
.sidebar {
    width: 250px;
    min-width: 150px;
    max-width: 500px;
    padding: 15px; /* Internal padding for the panel */
    box-sizing: border-box;
    flex-shrink: 0;
    -webkit-mask-image: none !important;
    mask-image: none !important;
}

#change-history-sidebar {
    width: 200px;
}

/* --- Resizer --- */
.resizer {
    width: 5px;
    flex-shrink: 0;
    cursor: col-resize;
    background-color: transparent; /* Make it invisible, the gap is the interaction area */
    z-index: 2;
    transition: background-color 0.2s;
    position: relative;
    margin: 0 5px;
}

.resizer:hover::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 1px;
    width: 3px;
    background-color: var(--highlight-text);
    border-radius: 3px;
}


/* --- Sidebar Content --- */
.sidebar h3 {
    margin-top: 0;
    padding: 0 8px;
    font-size: 1.1em;
    font-weight: 600;
    margin-bottom: 10px;
}

#historyList, #changeHistoryList {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
    /* FIX: Remove potential mask from inherited styles */
    -webkit-mask-image: none !important;
    mask-image: none !important;
}

#changeHistoryList::before, #changeHistoryList::after {
    display: none !important;
    background: none !important;
    content: '' !important;
}

#historyList li, #changeHistoryList li {
    padding: 8px 12px;
    cursor: pointer;
    border-radius: 6px;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    transition: background-color 0.2s, color 0.2s;
}

#historyList li:hover, #changeHistoryList li:hover {
    background-color: var(--button-hover-bg);
}

#historyList li.active, #changeHistoryList li.active {
    background-color: var(--accent-bg); /* This variable should be semi-transparent */
    color: var(--text-on-accent);
}

#historyList li .rename-input {
    width: calc(100% - 16px);
    padding: 6px;
    border: 1px solid var(--accent-bg);
    background-color: var(--input-bg);
    color: var(--primary-text);
    border-radius: 4px;
    font-family: inherit;
    font-size: inherit;
}

/* --- Editor Area --- */
.editor-container {
    flex-grow: 1;
    position: relative;
    /* The .glass-panel class handles the background and border */
}

.editor-top-bar {
    flex-shrink: 0;
    height: 40px;
    background-color: transparent; /* Part of the panel now */
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 15px;
    gap: 5px; /* Reduced gap between buttons */
}

.status-bar {
    height: 25px;
    background-color: transparent; /* Part of the panel now */
    border-top: 1px solid var(--border-color);
    padding: 0 15px;
    display: flex;
    align-items: center;
    font-size: 12px;
    flex-shrink: 0;
}

#filePath, #errorInfo {
    margin-right: 20px;
}

/* --- Editor (CodeMirror) --- */
.editor-wrapper {
    flex-grow: 1;
    overflow: hidden; /* Let CodeMirror handle its own scrolling */
    position: relative;
    /* FIX: Ensure flex item can shrink and grow properly */
    min-height: 0;
    display: flex; /* Make this a flex container */
    flex-direction: column; /* Stack children vertically */
}

#editor {
    flex-grow: 1;
    width: 100%;
    height: 100%;
    border: none;
    outline: none;
}

.CodeMirror {
    flex-grow: 1; /* Allow CodeMirror to fill the wrapper */
    height: auto; /* Override fixed height */
    background-color: transparent !important; /* Make CM background transparent */
}

.CodeMirror-gutters {
    background-color: transparent !important;
    border-right: 1px solid var(--border-color) !important;
}

/* --- Buttons --- */
.action-btn {
    background-color: var(--button-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 2px 10px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: none; /* Hidden by default */
}

.action-btn:hover {
    background-color: var(--button-hover-bg);
}

.global-action-button {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 18px;
    background-color: var(--button-bg);
    color: var(--primary-text);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s;
    font-size: 14px;
    text-align: center;
    margin-top: auto; /* Push to bottom */
}

.global-action-button:hover {
    background-color: var(--button-hover-bg);
}

/* --- Context Menus --- */
.custom-context-menu {
    position: absolute;
    display: none; /* Hidden by default, shown by JS */
    background-color: var(--panel-bg); /* Use glass panel background */
    backdrop-filter: blur(12px) saturate(150%);
    -webkit-backdrop-filter: blur(12px) saturate(150%);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    z-index: 1001;
    padding: 5px;
}

.custom-context-menu button {
    background: none;
    border: none;
    color: var(--primary-text);
    cursor: pointer;
    padding: 5px 10px;
    width: 100%;
    text-align: left;
    border-radius: 4px;
}

.custom-context-menu button:hover {
    background-color: var(--accent-bg);
}

.custom-context-menu hr {
    border-color: var(--border-color);
    margin: 5px 0;
}

/* --- Scrollbar --- */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}