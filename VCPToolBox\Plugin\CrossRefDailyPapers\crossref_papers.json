[{"doi": "10.1007/978-***********-6_13", "title": "Expanding Range and Flexibility: Reference-Free Radar Networks for Multi-target Detection", "authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.5511/plantbiotechnology.25.0504a", "title": "Developmental stage-specific triterpenoid saponin accumulations in Ardisia crenata rhizosphere and its influence on rhizosphere microbial communities", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Japanese Society for Plant Cell and Molecular Biology", "type": "journal-article", "journal": "Plant Biotechnology"}, {"doi": "10.1007/978-3-032-00656-1_27", "title": "Detection of Multiple Cardiac Disorders Based on Heartbeat Morphology and Time Segment Analysis of ECG Signals", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Switzerland", "type": "book-chapter", "journal": "Lecture Notes in Computer Science, Artificial Intelligence in Healthcare"}, {"doi": "10.1007/s00508-025-02599-3", "title": "Balancing innovation and interpretation: evaluating artificial intelligence in adenoma detection", "authors": "Nathkapach Kaewpitoon Rattanapitoon; Phatsakul Thitimahatthanakusol; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>oon Rattanapitoon", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Science and Business Media LLC", "type": "journal-article", "journal": "Wiener klinische Wochenschrift"}, {"doi": "10.20517/mrr.2025.25", "title": "Recent advances in gut microbiota-mediated regulation of fat deposition and metabolic disorders", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "The gut microbiota critically regulates lipid metabolism through microbial metabolites and host signaling pathways. Short-chain fatty acids (SCFAs), derived from dietary fiber fermentation, suppress hepatic lipogenesis via inhibition of SREBP-1c and enhance mitochondrial β-oxidation through GPR41/43 activation. Microbial enzymes convert primary bile acids into secondary bile acids, which activate FXR to inhibit lipogenesis and TGR5 to promote adipose thermogenesis. Lipopolysaccharide (LPS) from dysbiotic microbiota triggers TLR4-NF-κB signaling, exacerbating insulin resistance and adipose inflammation. Branched-chain amino acids (BCAAs), metabolized by gut microbes, drive adipogenesis via mTORC1-PPARγ signaling, with elevated circulating BCAAs linked to obesity. In livestock, microbiota modulation optimizes fat deposition: probiotics in pigs enhance intramuscular fat via Lactobacillus-enriched communities, while dietary succinate or coated sodium propionate reduces abdominal fat in broilers by reshaping cecal microbiota. Fecal microbiota transplantation confirms microbial causality in transferring fat phenotypes. Dysbiosis-associated mechanisms are conserved across species, where SCFAs and bile acids ameliorate metabolic inflammation, whereas LPS and BCAA imbalances worsen lipid dysregulation. Metabolic disorders, including obesity, type 2 diabetes (T2D), and non-alcoholic fatty liver disease (NAFLD), are tightly linked to gut microbiota perturbations. Dysbiosis drives LPS translocation and barrier impairment. These changes, along with altered metabolites, promote inflammation and fat deposition. Future strategies should integrate multi-omics and precision engineering of microbial consortia to advance therapies for both livestock and human metabolic health.", "publisher": "OAE Publishing Inc.", "type": "journal-article", "journal": "Microbiome Research Reports"}, {"doi": "10.1007/978-981-95-1525-7_16", "title": "Design, Implementation, and Application of a 3-D Virtual Simulation Visualization Experiment System for Camera Calibration", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Educational Innovation Through Technology"}, {"doi": "10.20965/jrm.2025.p0918", "title": "Modeling and Experimental Analysis for <PERSON><PERSON> Loosening Detection Using a High-Speed Robotic Hand", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "High-speed robotic hands are capable of agile movements and short-cycle control and can perform a variety of actions that are not possible with ordinary robotic hands.  In this study, this characteristic is focused on and verified for the construction of an algorithm to detect screw loosening from force sensor data acquired when an object is vibrated at high-speeds.  First, a simple model of the system was developed by applying vibrations to a part connected by a screw to a robotic hand.  Based on the constructed model, simulations were performed to confirm weather the presence or absence of screw loosening influenced force data.   Subsequently, experiments were conducted using a robotic hand and models to verify the differences caused by screw loosening.  These results confirmed the differences in the force data available for detecting screw loosening.   Furthermore, to confirm the reliability of the constructed model and consider the inspection method, the grasping position of the inspected object was added as a condition for verification.  This additional experiment confirmed that there were significant changes in the force data depending on the grasping position.  Additionally, the optimal grasping conditions for the inspection method were discussed, and the validity of the method was confirmed based on simple discrimination results.   In the future, simulations using models with extended degrees of freedom, along with further experimental verification, will be conducted to develop more accurate inspection methods.", "publisher": "Fuji Technology Press Ltd.", "type": "journal-article", "journal": "Journal of Robotics and Mechatronics"}, {"doi": "10.54254/2755-2721/2025.26117", "title": "SUD-YOLO:A Stable Underwater Target Detection Algorithm Based on Sampling Improved YOLOv11", "authors": "<PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "In the field of target detection, underwater target detection (UTD) still faces many challenges. Although YOLO11 shows excellent real-time detection performance, its direct application in UTD is not satisfactory because it has not been designed for complex scenarios such as excessive object deformation and blurred lighting in underwater environments, and is unable to fully extract and utilize the effective information in images, resulting in low detection accuracy. To overcome this drawback, we developed a new detection model SUD-YOLO (Stable Underwater Detection) based on YOLOv11 to improve the detection accuracy and stability for underwater objects. Compared with YOLOv11, SUD-YOLO provides SRFD (Shallow Robust Feature Downing-sampling) and DRFD (Deep Robust Feature Downing-sampling) modules, which alleviate the problem of important information loss during the deep propagation process due to sampling (Upsampling and Downsampling) or multi-layer convolution by input feature scaling fusion. At the same time, EfficientHead is adopted instead of the traditional mixed detection head to ensure that the output features are not mutually dependent. Experimental results on the URPC2020, Luderick and Deepfish datasets prove that SUD-YOLO has higher stability and faster convergence during training, demonstrating excellent UTD performance. This research proposes an efficient and reliable method for UTD tasks, providing technical support for underwater exploration and marine resource investigation, and contributing to the development of underwater intelligent detection.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.1253/circrep.cr-25-0134", "title": "Microbial Transitions in Infective Endocarditis Influence the Spectrum of Clinical Features and Functional Outcomes", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Japanese Circulation Society", "type": "journal-article", "journal": "Circulation Reports"}, {"doi": "10.5511/plantbiotechnology.25.0529a", "title": "&lt;i&gt;Colletotrichum &lt;/i&gt;&lt;i&gt;tofieldiae&lt;/i&gt; enhances phosphorus uptake and biomass production and alters the microbial interactions in the rhizosphere of Komatsuna (&lt;i&gt;Brassica rapa&lt;/i&gt; var. perviridis) grown in phosphorus-deficient farm soils", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Japanese Society for Plant Cell and Molecular Biology", "type": "journal-article", "journal": "Plant Biotechnology"}, {"doi": "10.54254/2755-2721/2025.26124", "title": "Optimizing AdaBoost for Bitcoin Price Prediction Based on Long and Short Term Memory Network Models", "authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "In this paper, a hybrid time series analysis model for bitcoin price forecasting is constructed by introducing a long short-term memory network (LSTM) to deeply optimize the traditional AdaBoost integrated learning model. Experimental results show that the fusion model exhibits excellent dual adaptability in the field of financial time series forecasting: in the training phase, the model achieves a high degree of fit to historical data with an excellent performance of MAE 1.3574, MSE 3.4279, and MAPE 0.412%, and its coefficient of determination, R, breaks through 0.97105, which intuitively verifies the ability of the LSTM module in capturing the non-linear time-series characteristics of the The synergy between the LSTM module and the AdaBoost framework in strengthening the generalization ability of the model; in the testing phase, although facing the challenge of increased market volatility, the model still maintains a stable performance of MAE 1.914, RMSE 2.7893 and MAPE 0.586%, especially the prediction error rate is continuously controlled within 1% of the industrial-grade accuracy threshold, and its test set R value of 0.88675 significantly exceeds the benchmark interval of 0.6-0.8 for conventional prediction systems, confirming the strong explanatory power and robustness of the model in unknown data environments.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.1186/s12916-025-04304-7", "title": "Effect of discontinuing antipsychotic medications on the risk of hospitalization in long-term care: a machine learning-based analysis", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Science and Business Media LLC", "type": "journal-article", "journal": "BMC Medicine"}, {"doi": "10.1007/978-***********-4_32", "title": "Aviation Medicine Emergency Rescue Simulation Platform Pose Anomaly Detection Model", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.20965/jrm.2025.p0852", "title": "Real-Time Multi-Viewpoint Object Detection for High-Speed Omnidirectional Scanning Shooting System", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Obtaining object-detection results through multi-viewpoint systems is a promising approach for gathering the surrounding information for mobile robots. In this study, we realized a low delay real-time multi-viewpoint object detection for an omnidirectional scanning shooting system by proposing a dual-stage detection approach with intermediate frames. This approach can synchronously search for valid viewpoints containing the desired objects, and perform low-update-delay real-time object detection on selected viewpoints with intermediate frames. We generated panoramic detection frames as real-time system output by projecting the individual viewpoint detection results. In an outdoor experimental scenario with 10 fps omnidirectional scanning shooting accommodating up to 40 viewpoints, our approach generated 20 fps real-time panoramic detection frames presenting the movement of the observed targets.", "publisher": "Fuji Technology Press Ltd.", "type": "journal-article", "journal": "Journal of Robotics and Mechatronics"}, {"doi": "10.1007/978-***********-5_12", "title": "An Improved YOLOv8 Object Detection Method for Images Recognition for Wearable AR Smart Maintenance", "authors": "<PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.1093/9780198974550.003.0010", "title": "Long-term effects of pediatric cochlear implantation", "authors": "<PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Abstract\n               This chapter summarizes what is known about long-term effects of pediatric cochlear importation. How long do the benefits of cochlear implants (CIs) in the early years prevail and how do these benefits decline in adolescence and adulthood? How does pediatric cochlear implantation ultimately effect academic achievement and functioning in adulthood? First, we address studies that consider aspects of device use as we assume an association between device use and long-term outcomes. With respect to academic achievement, we then focus on school placement (and thus type of education), outcomes, and grade failure. With respect to functioning in adulthood, we focus on employment and explore whether receiving CIs in childhood is in any way related to employment status and job satisfaction in adulthood? Finally, we revisit the impact of age of implantation, this time on long-term effects of cochlear implantation. The chapter concludes by acknowledging the limited and varied nature of research on the long-term effects of pediatric cochlear implantation. It emphasizes the need for more comprehensive and comparative research to fully understand the impact of CI use on academic and employment outcomes.", "publisher": "Oxford University PressOxford", "type": "book-chapter", "journal": "Growing Up with Cochlear Implants"}, {"doi": "10.1007/978-3-032-00652-3_18", "title": "Translating Genes into Insight: Causal Genomics for Diabetes Risk Prediction", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Switzerland", "type": "book-chapter", "journal": "Lecture Notes in Computer Science, Artificial Intelligence in Healthcare"}, {"doi": "10.1186/s12916-025-04335-0", "title": "“Have you considered that it could be burnout?”—psychologization and stigmatization of self-reported long COVID or post-COVID-19 vaccination syndrome", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "Abstract\n          \n            Background\n            People reporting long COVID (LC) or post-COVID-19 vaccination syndrome (PCVS) not only suffer from their symptoms but also from stigmatization. Despite ample account and characterization of stigma experiences so far, its mechanisms and consequences on health outcomes, and particularly the role of “psychologization” remain unclear.\n          \n          \n            Methods\n            In a cross-sectional observational study, we examined a large convenience sample of adults who report having LC or PCVS. We translated and adapted the “Long Covid Stigma Scale” to measure stigmatization. We measured generally perceived and personally experienced psychologization with newly developed scales/items. Outcome measures included disclosure concerns, loss of trust in medicine, life satisfaction, depression, anxiety, self-esteem, and loneliness. We calculated overall prevalences of stigma and psychologization and their correlations with the outcomes. Using mediation analysis with SEM, we tested the hypothesis that psychologization of LC and PCVS syndromes causes harm by increasing stigmatization.\n          \n          \n            Results\n            Altogether, N = 2053 individuals (68% reporting LC, 32% reporting PCVS) were included in the analyses. The overall prevalences of stigma experiences were high: 83% of those reporting LC and 90% of those reporting PCVS experienced stigma. Prevalences of perceived psychologization (LC: 87%, PCVS: 91%) and experienced psychologization (LC: 82%, PCVS: 87%) were similarly high. Both stigmatization and psychologization were positively correlated with disclosure concerns, loss of trust in medicine, depression, anxiety, and loneliness as well as negatively correlated with life satisfaction and self-esteem. Mediation analysis indicated that stigmatization mediated a relevant proportion of the relationship between psychologization and negative outcomes.\n          \n          \n            Conclusions\n            People reporting LC or PCVS are subject to stigmatization and psychologization. From a patient perspective, psychologization appears to be an important driver of stigmatization and negative outcomes.", "publisher": "Springer Science and Business Media LLC", "type": "journal-article", "journal": "BMC Medicine"}, {"doi": "10.1007/978-3-032-01589-1_11", "title": "GovTech Incubators: Bridging the Gap Between Prototypes and Long-Term Implementation", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Switzerland", "type": "book-chapter", "journal": "Lecture Notes in Computer Science, Electronic Government"}, {"doi": "10.54254/2755-2721/2026.ka26173", "title": "Integrated Solar Energy Utilization and Conduction Filtration SystemMedusozoa", "authors": "<PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "With rapid urbanization, many low-rise residential buildings and certain indoor spaces suffer from inadequate natural lighting, relying heavily on artificial illumination. This dependence leads to increased energy consumption and potential exposure to harmful ultraviolet radiation. Existing indoor lighting systems typically use fixed light sources with limited adjustment capabilities, resulting in inefficient use of natural light and lacking ultraviolet filtration, which poses both energy and health challenges. To address these issues, this study proposes the \"Medusozoa\" intelligent solar energy utilization system, which achieves indoor natural lighting through efficient light collection, intelligent sunlight tracking, ultraviolet filtration, and optical fiber conduction technologies. The system employs a light sensor, an Arduino microcontroller, and a servo motor to track the sun's angle in real time, maximizing sunlight collection. It filters out harmful ultraviolet light and efficiently channels the filtered light indoors through optical fibers, significantly enhancing energy efficiency while ensuring health and safety.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.1051/0004-6361/202554595", "title": "Detection of millimetre-wave coronal emission in a quasar at cosmological distance using microlensing", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Determining the nature of emission processes at the heart of quasars is critical for understanding environments of supermassive black holes. One of the key open questions is the origin of centimetre- to millimetre-wave emission from radio-quiet quasars. The proposed mechanisms range from central star formation to dusty torus, low-power jets, or emission from the accretion-disc corona. Distinguishing between these scenarios requires probing spatial scales of łeq0.01 pc, beyond the reach of any current millimetre-wave telescope. Fortunately, in gravitationally lensed quasars, compact millimetre-wave emission might be microlensed by stars in the foreground galaxy, providing strong constraints on the source size. We report a striking change in rest-frame 1.3 mm flux ratios in RXJ1131-1231, a quadruply lensed quasar at z=0.658 observed by the Atacama Large Millimeter/submillimeter Array (ALMA) in 2015 and 2020. Over this period, the flux ratios between the three quasar images, A, B, and C, changed by a factor of 1.6 (A/B) and 3.0 (A/C). The observed flux-ratio variability is consistent with the microlensing of a compact source with a half-light radius of łeq50 astronomical units. The compactness of the source leaves coronal emission as the most likely scenario. Furthermore, the inferred millimetre-wave and X-ray luminosities follow the Güdel-Benz relationship for stellar coronae. These observations represent the first unambiguous evidence that coronae are the dominant mechanism for centimetre- to millimetre-wave emission in radio-quiet quasars.", "publisher": "EDP Sciences", "type": "journal-article", "journal": "Astronomy &amp; Astrophysics"}, {"doi": "10.1007/978-***********-4_34", "title": "Android Malware Detection Method Based on Multi-feature Extraction and SPP-Net", "authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.54254/2755-2721/2025.po26094", "title": "Research on Seizure Detection Using EEG Signals Based on Multi-Scale Convolutional Neural Networks BiLSTM-Multi-Head Self-Attention", "authors": "<PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Traditional methods for epileptic seizure detection suffer from limitations such as insufficient feature extraction capability, high computational complexity, and inadequate generalization performance. In this study, leveraging Electroencephalogram (EEG) signals, a novel epileptic seizure detection method based on the Multi-Scale Convolutional Neural Networks-BiLSTM-Multi-Head self-attention(MSCNN-BiLSTM-MHSA) model is proposed. The MSCNN-BiLSTM-MHSA model comprehensively extracts features of EEG signals at different scales by constructing an improved multi-scale convolutional neural network. Furthermore, the introduction of the multi-head self-attention mechanism enables the model to focus more on key features during the iteration process, thereby enhancing the feature learning capability. Experiments were conducted on two epileptic datasets, namely CHB-MIT and Bonn, for validation. The results demonstrate that the MSCNN-BiLSTM-MHSA model achieves an accuracy of 96.23% and 96.13% respectively in epileptic seizure detection.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.1007/978-***********-6_19", "title": "Deep Learning-Based Traffic Detection Method Using m-Sequence Word Embedding and Boruta Feature Selection on Adjusting Attention Mechanisms", "authors": "<PERSON><PERSON>; Fangning Shi; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.1007/978-***********-6_20", "title": "Deep Learning-Based Fault Detection Method Using Convolutional Code Word Embedding and Maximum Mutual Information Attention Mechanism", "authors": "<PERSON><PERSON>; <PERSON><PERSON>; <PERSON><PERSON>ng Mu", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.20965/jrm.2025.p0945", "title": "Automatic Driving Experiment of a Personal Vehicle Using Detection of Steady-State Visual Evoked Potentials by Measurement Objects in Mixed Reality", "authors": "<PERSON><PERSON><PERSON>; <PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "With the aging population, personal vehicles (PVs) have become widely used and are important mobility devices for the elderly. Driving a PV for the elderly involves risks such as collisions. Therefore, driving assistance and a training system utilizing mixed reality (MR) have been developed. However, as these systems are controlled manually, safe operation cannot be guaranteed, particularly for users who are not good at operating them. In this study, an autonomous driving system for a PV using a brain-computer interface is proposed. Measurement objects generating flicker stimuli are projected onto the MR device, and steady-state visual evoked potentials in the visual cortex are detected. Using the automatic driving system, safe operation is realized without stopping, as the intention of the traveling direction of the PV is estimated. The effectiveness of the proposed system is demonstrated through experiments, and the driving workloads are evaluated using NASA-TLX.", "publisher": "Fuji Technology Press Ltd.", "type": "journal-article", "journal": "Journal of Robotics and Mechatronics"}, {"doi": "10.1186/s13059-025-03718-z", "title": "TRsv: simultaneous detection of tandem repeat variations, structural variations, and short indels using long read sequencing data", "authors": "<PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Science and Business Media LLC", "type": "journal-article", "journal": "Genome Biology"}, {"doi": "10.1007/s12083-025-01907-y", "title": "Enhancing cybersecurity through hybrid blockchain-enabled intrusion detection systems: A machine learning approach", "authors": "<PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Science and Business Media LLC", "type": "journal-article", "journal": "Peer-to-Peer Networking and Applications"}, {"doi": "10.1007/978-***********-5_20", "title": "Small Object Detection Based on Self-attention and Multi-scale Feature Fusion", "authors": "<PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.54254/2755-2721/2025.ld26122", "title": "The Lane Detection Model Based on Data Augmentation and Deep Learning", "authors": "<PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Lane detection serves as a cornerstone task in autonomous driving systems, as it directly impacts the vehicles ability to maintain lane discipline, ensure safety, and perform accurate path planning. Although U-Net-based deep learning models have demonstrated strong potential for automatic lane segmentation, their performance can degrade significantly under complex real-world conditions such as variable lighting, occlusions, and worn or curved lane markings.To address these limitations, this study proposes an enhanced lane detection framework built upon the U-Net architecture. The proposed model integrates three key improvements: (1) advanced data augmentation techniques to increase the diversity and robustness of the training data, (2) a refined loss function combining PolyLoss and contrastive loss to address foreground-background imbalance and enhance structural learning, and (3) an optimized upsampling strategy designed to better preserve spatial details and lane continuity in the output predictions.Extensive experiments conducted on the TuSimple lane detection benchmark validate the effectiveness of our approach. The enhanced model achieves an Intersection over Union (IoU) of 44.49%, significantly surpassing the baseline U-Nets performance of 40.36%. These results confirm that the proposed modifications not only improve segmentation accuracy but also enhance the models robustness and generalization capability in real-world driving scenarios. Overall, this work contributes practical insights and techniques that can facilitate the deployment of lane detection systems in intelligent transportation and autonomous vehicle platforms.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.54254/2755-2721/2025.26120", "title": "Optimization of YOLOv8 Traffic Sign Object Detection Based on BiFPN Feature Pyramid and CBAM Attention Module", "authors": "<PERSON><PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "This study proposes the integration of BiFPN feature pyramid and CBAM attention module in YOLOv8 to enhance the robustness of traffic sign and signal detection, based on the urgent need for urban road safety and autonomous driving. The experiment was validated on a test set of 801 images and 944 targets, and the overall precision of the model reached 0.739, Recall 0.654mAP50 0.723mAP50-95 0.631 Significantly better than the baseline, with improvements of 5.2%, 1%, 2.8%, and 2.15% respectively, confirming that the improvement strategy effectively reduces false positives and improves localization classification consistency. The subdivision results show that the Stop logo achieves almost zero missed detections due to its high contrast and regular shape, with Precision and mAP50 both approaching 1; The mAP50 of the three speed limits of 20, 60, and 70 km/h all exceeded 0.82 under sufficient sample conditions, and remained above 0.75 on the stricter mAP50-95 index, indicating good generalization to scale and lighting changes; Although data is scarce for speed limits of 100 and 120 km/h, mAP50 still reaches 0.77 and 0.85, indicating that the network has fully learned the common features of circular speed limit signs; In contrast, signal lights such as Red Light have a small scale and complex background, with mAP50-95 less than 0.35 and low recall, making them a key focus for future optimization. Overall, the current model has matured for high contrast and regular shape signs. The next step should be to focus on improving the recall rate of small sample categories and traffic lights through difficult case mining, multi-scale training, and targeted data augmentation. The gap between mAP50 and mAP50-95 should be narrowed at higher IoU thresholds to meet the high reliability requirements of real road scenes. This study not only validates the effectiveness of BiFPN+CBAM in traffic sign detection, but also provides a reference for improving low sample category and small object detection, which has positive significance for promoting the safe implementation of intelligent transportation systems and autonomous driving.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}, {"doi": "10.1007/978-***********-5_16", "title": "Network Flow Watermarking Segmentation Detection Based on Abstract Feature Extraction", "authors": "<PERSON><PERSON>; <PERSON>; <PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.1007/978-***********-4_30", "title": "Mobile Node-Based Vulnerability Detection and Repair Design for Wireless Sensor Networks", "authors": "<PERSON>; <PERSON><PERSON><PERSON>; <PERSON><PERSON><PERSON>; <PERSON>; <PERSON>; <PERSON>n<PERSON>g <PERSON>v", "issuedDate": "2025-8-20", "abstract": "N/A", "publisher": "Springer Nature Singapore", "type": "book-chapter", "journal": "Communications in Computer and Information Science, Information Processing and Network Provisioning"}, {"doi": "10.54254/2755-2721/2025.26082", "title": "MA-YOLOv8: Road Vehicle Target Detection Based on MBConv Module and C2f_AKConv Structure Improved YOLOv8", "authors": "<PERSON><PERSON>", "issuedDate": "2025-8-20", "abstract": "Rapid, accurate and robust detection of road vehicles is a core and fundamental task in fields such as intelligent transportation systems, autonomous driving and traffic management. In response to this demand, this paper proposes an improved YOLOv8 model, which optimizes feature extraction and scale adaptability by introducing the MBConv module and the C2f_AKConv structure. The improved model has achieved a significant improvement in overall detection performance compared to the original model: the average accuracy (mAP50-95) has been greatly enhanced from 0.352 to 0.466. The recall rate (R) increased from 0.421 to 0.539, significantly reducing the rate of missed detections. The precision rate (P) has increased from 0.632 to 0.735, effectively reducing false detections. In terms of specific categories, the improvements are particularly prominent: the recall rate of the Car category has jumped from 0.252 to 0.328, and the mAP50 has increased from 0.392 to 0.474, enhancing the ability to recognize occlusions and small targets. The mAP50-95 of the Truck category has been upgraded from 0.183 to 0.264, improving the detection of vehicles with complex shapes. The Ambulance category maintained a mAP50 above 0.9 while MAP50-95 was raised to 0.799, achieving more accurate bounding box localization. The comprehensive results show that the proposed improvement strategy effectively enhances the feature extraction ability, scale adaptability and detection robustness for weak texture targets of the model. This research provides a more effective solution for real-time target detection in complex traffic scenarios.", "publisher": "EWA Publishing", "type": "journal-article", "journal": "Applied and Computational Engineering"}]