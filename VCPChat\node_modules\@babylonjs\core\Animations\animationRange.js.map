{"version": 3, "file": "animationRange.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animationRange.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,cAAc;IACvB;;;;;OAKG;IACH;IACI,qCAAqC;IAC9B,IAAY;IACnB,yCAAyC;IAClC,IAAY;IACnB,sCAAsC;IAC/B,EAAU;QAJV,SAAI,GAAJ,IAAI,CAAQ;QAEZ,SAAI,GAAJ,IAAI,CAAQ;QAEZ,OAAE,GAAF,EAAE,CAAQ;IAClB,CAAC;IAEJ;;;OAGG;IACI,KAAK;QACR,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7D,CAAC;CACJ", "sourcesContent": ["/**\r\n * Represents the range of an animation\r\n */\r\nexport class AnimationRange {\r\n    /**\r\n     * Initializes the range of an animation\r\n     * @param name The name of the animation range\r\n     * @param from The starting frame of the animation\r\n     * @param to The ending frame of the animation\r\n     */\r\n    constructor(\r\n        /**The name of the animation range**/\r\n        public name: string,\r\n        /**The starting frame of the animation */\r\n        public from: number,\r\n        /**The ending frame of the animation*/\r\n        public to: number\r\n    ) {}\r\n\r\n    /**\r\n     * Makes a copy of the animation range\r\n     * @returns A copy of the animation range\r\n     */\r\n    public clone(): AnimationRange {\r\n        return new AnimationRange(this.name, this.from, this.to);\r\n    }\r\n}\r\n"]}