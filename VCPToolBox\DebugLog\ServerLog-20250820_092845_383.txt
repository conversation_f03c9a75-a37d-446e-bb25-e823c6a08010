[2025/8/20 09:28:45] Server log started.
[2025/8/20 09:28:45] [LOG] 共加载了 3 条系统提示词转换规则。
[2025/8/20 09:28:45] [LOG] 共加载了 4 条全局上下文转换规则。
[2025/8/20 09:28:45] [LOG] [SpecialRouter] 加载了 1 个图像白名单模型: gemini-2.0-flash-exp-image-generation
[2025/8/20 09:28:45] [LOG] [SpecialRouter] 加载了 1 个向量化白名单模型: gemini-embedding-exp-03-07
[2025/8/20 09:28:45] [LOG] [AdminPanelRoutes DEBUG] Attempting to register POST /admin_api/dailynotes/delete-batch
[2025/8/20 09:28:45] [LOG] 中间层服务器正在监听端口 6005
[2025/8/20 09:28:45] [LOG] API 服务器地址: http://10.0.0.40:3000
[2025/8/20 09:28:45] [LOG] 开始加载插件...
[2025/8/20 09:28:45] [LOG] [PluginManager] Starting plugin discovery...
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 代理消息推送插件 (AgentMessage, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: ArxivDailyPapers (ArxivDailyPapers, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Bilibili 内容获取插件 (BilibiliFetch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Chrome 浏览器控制器 (ChromeControl, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Chrome 浏览器观察者 (ChromeObserver, Type: service)
[2025/8/20 09:28:45] [LOG] [ChromeObserver] Initializing with config: {
  "DebugMode": true
}
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: ComfyUI 图像生成器 (ComfyUIGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: CrossRef Daily Papers (CrossRefDailyPapers, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 每日热榜 (DailyHot, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 日记内容编辑器 (DailyNoteEditor, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 日记内容获取器 (静态) (DailyNoteGet, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 日记整理器 (DailyNoteManager, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 日记写入器 (同步) (DailyNoteWrite, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Doubao 风格图片生成器 (DoubaoGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 表情包列表文件生成器 (EmojiListGenerator, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 文件列表生成器 (FileListGenerator, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 文件服务 (FileServer, Type: service)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 文件树生成器 (FileTreeGenerator, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 闪电深度研究插件 (FlashDeepSearch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Flux 风格图片生成器 (FluxGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: FRPS 设备信息提供器 (FRPSInfoProvider, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Gemini 图像生成与编辑 (GeminiImageGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 谷歌搜索 (API版) (GoogleSearch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 多模态数据提取器 (ImageProcessor, Type: messagePreprocessor)
[2025/8/20 09:28:45] [LOG] [MultiModalProcessor] Loaded 0 media cache entries from D:\VCP\VCPToolBox\Plugin\ImageProcessor\multimodal_cache.json
[2025/8/20 09:28:45] [LOG] [MultiModalProcessor] Initialized and cache loaded.
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 图床服务 (ImageServer, Type: service)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Karakeep 搜索书签 (KarakeepSearch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: NovelAI 图片生成器 (NovelAIGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 随机事件生成器 (Randomness, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 科学计算器 (SciCalculator, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Suno AI 音乐生成 (SunoGen, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: VCP 日志 Synapse 推送器 (SynapsePusher, Type: service)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 塔罗占卜 (TarotDivination, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: Tavily 搜索插件 (TavilySearch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: URL 内容获取插件 (UrlFetch, Type: synchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: VCP 日志推送插件 (VCPLog, Type: service)
[2025/8/20 09:28:45] [LOG] [VCPLog] Initializing with config: {
  "VCP_Key": "WEB_SOCKET_8xIUMi8S&^Os0%e8",
  "DebugMode": true
}
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: VCP 上下文注入器 (VCPTavern, Type: hybridservice)
[2025/8/20 09:28:45] [LOG] [VCPTavern] 已加载预设: example
[2025/8/20 09:28:45] [LOG] [VCPTavern] 插件已初始化。
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 视频生成器 (Wan2.1) (Wan2.1VideoGen, Type: asynchronous)
[2025/8/20 09:28:45] [LOG] [PluginManager] Loaded manifest: 天气预报员 (WeatherReporter, Type: static)
[2025/8/20 09:28:45] [LOG] [PluginManager] Plugin discovery finished. Loaded 37 plugins.
[2025/8/20 09:28:45] [LOG] 插件加载完成。
[2025/8/20 09:28:45] [LOG] 开始初始化服务类插件...
[2025/8/20 09:28:45] [LOG] [PluginManager] Initializing service plugins...
[2025/8/20 09:28:45] [LOG] [ChromeObserver] Registering routes...
[2025/8/20 09:28:45] [LOG] [FileServerPlugin] Registering routes for FileServer. DebugMode is ON.
[2025/8/20 09:28:45] [LOG] [FileServerPlugin] Protected file service registered. Access path format: /pw=FIL***0@Q/files/...
[2025/8/20 09:28:45] [LOG] [FileServerPlugin] Serving main directory from: D:\VCP\VCPToolBox\file
[2025/8/20 09:28:45] [LOG] [FileServerPlugin] Serving special directory 'doubaogen' from: D:\VCP\VCPToolBox\image\doubaogen
[2025/8/20 09:28:45] [LOG] [FileServerPlugin] Serving special directory 'fluxgen' from: D:\VCP\VCPToolBox\image\fluxgen
[2025/8/20 09:28:45] [LOG] [ImageServerPlugin] Registering routes. DebugMode is ON.
[2025/8/20 09:28:45] [LOG] [ImageServerPlugin] Protected image service registered. Access path: /pw=IMA***yr*/images/... serving from D:\VCP\VCPToolBox\image
[2025/8/20 09:28:45] [LOG] [ImageServerPlugin] Protected file service registered. Access path: /pw=FIL***0@Q/files/... serving from D:\VCP\VCPToolBox\file
[2025/8/20 09:28:45] [WARN] [SynapsePusher] MaidAccessTokensJSON is not defined in config.
[2025/8/20 09:28:45] [WARN] [SynapsePusher] MaidToolWhitelistJSON is not defined in config. All tools will be allowed by default for maids with valid tokens.
[2025/8/20 09:28:45] [LOG] [SynapsePusher] registerRoutes called. Initialized config: {
  "DebugMode": true,
  "VCP_Key": "WEB_SOCKET_8xIUMi8S&^Os0%e8",
  "SERVER_PORT": "6005",
  "PROJECT_BASE_PATH": "D:\\VCP\\VCPToolBox"
}
[2025/8/20 09:28:45] [LOG] [SynapsePusher] Plugin loaded. Attempting to connect to WebSocket log source if config is present.
[2025/8/20 09:28:45] [LOG] [SynapsePusher] DEBUG: Current VCP_Key from config: "WEB_SOCKET_8xIUMi8S&^Os0%e8"
[2025/8/20 09:28:45] [LOG] [SynapsePusher] DEBUG: Current SERVER_PORT from config: "6005"
[2025/8/20 09:28:45] [LOG] [SynapsePusher] Attempting to connect to VCPLog WebSocket source at: ws://localhost:6005/VCPlog/VCP_Key=WEB_SOCKET_8xIUMi8S&^Os0%e8
[2025/8/20 09:28:45] [LOG] [VCPLog] registerRoutes called. Log file initialized. WebSocket setup is handled by WebSocketServer.js.
[2025/8/20 09:28:45] [LOG] [VCPTavern] API 路由已通过 adminApiRouter 注册到 /vcptavern
[2025/8/20 09:28:45] [LOG] [PluginManager] Service plugins initialized.
[2025/8/20 09:28:45] [LOG] 服务类插件初始化完成，管理面板 API 路由已挂载。
[2025/8/20 09:28:45] [LOG] 开始初始化静态插件...
[2025/8/20 09:28:45] [LOG] [PluginManager] Initializing static plugins...
[2025/8/20 09:28:45] [LOG] [PluginManager] Static plugins initialization process has been started (updates will run in the background).
[2025/8/20 09:28:45] [LOG] 静态插件初始化完成。
[2025/8/20 09:28:45] [LOG] 开始从插件目录加载表情包列表到缓存 (由EmojiListGenerator插件生成)...
[2025/8/20 09:28:45] [LOG] [initialize] Found 2 emoji list files in D:\VCP\VCPToolBox\Plugin\EmojiListGenerator\generated_lists. Loading...
[2025/8/20 09:28:45] [LOG] [VCPLog] Log directory and file ensured at: D:\VCP\VCPToolBox\Plugin\VCPLog\log\VCPlog.txt
[2025/8/20 09:28:45] [ERROR] [SynapsePusher] WebSocket client error connecting to VCPLog source: Unexpected server response: 401
[2025/8/20 09:28:45] [LOG] [SynapsePusher] Attempting WebSocket reconnect to VCPLog source due to direct error.
[2025/8/20 09:28:45] [WARN] [SynapsePusher] WebSocket client to VCPLog source disconnected. Code: 1006, Reason: . Attempting to reconnect...
[2025/8/20 09:28:45] [LOG] [initialize] All available emoji lists loaded into cache.
[2025/8/20 09:28:45] [LOG] 表情包列表缓存加载完成。
[2025/8/20 09:28:45] [LOG] 正在初始化通用任务调度器...
[2025/8/20 09:28:45] [LOG] [TaskScheduler] 启动目录监视: D:\VCP\VCPToolBox\VCPTimedContacts
[2025/8/20 09:28:45] [LOG] 通用任务调度器已初始化并开始监视任务。
[2025/8/20 09:28:45] [LOG] [Server] Initializing WebSocketServer...
[2025/8/20 09:28:45] [LOG] [WebSocketServer] Initialized. Waiting for HTTP server upgrades.
[2025/8/20 09:28:45] [LOG] [WebSocketServer] PluginManager instance has been set.
[2025/8/20 09:28:45] [LOG] [FileFetcherServer] Initialized and linked with WebSocketServer.
[2025/8/20 09:28:45] [LOG] [Server] WebSocketServer, PluginManager, and FileFetcherServer have been interconnected.
[2025/8/20 09:28:45] [LOG] [TaskScheduler] 未发现待处理的定时任务。调度器将保持待命。
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.050Z - Distributed Server attempting to connect.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.051Z - Distributed Server dist-mejanul7-852dvax authenticated and connected.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] Client mejanul7-852dvax connected.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.053Z - VCPLog client attempting to connect.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.053Z - Client mejanul9-vn7blht (Type: VCPLog) authenticated and connected.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] Client mejanul9-vn7blht connected.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] Received message from mejanul7-852dvax (DistributedServer): {"type":"register_tools","data":{"serverName":"VCP-Desktop-Client-Distributed-Server","tools":[{"name":"DeepMemo","displayName":"深度回忆插件","version":"1.0.0","description":"根据关键词从Vchat聊天记录中检索相关上下文，实现AI的深度回忆功能。","pluginType":"synchronous","communication":{"protocol":"stdio"},"entryPoint":{"command":"nod...
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.058Z - Received message from Distributed Server dist-mejanul7-852dvax: {"type":"register_tools","data":{"serverName":"VCP-Desktop-Client-Distributed-Server","tools":[{"name":"DeepMemo","displayName":"深度回忆插件","version":"1.0.0","description":"根据关键词从Vchat聊天记录中检索相关上下文，实现AI的深...
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 深度回忆插件 (DeepMemo) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 分布式图床服务器 (DistImageServer) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 文件操作器 (FileOperator) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 音乐播放器控制器 (MusicController) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 米家台灯遥控器 (TableLampRemote) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 本地文件秒搜 (Everything) (LocalSearchController) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 超级骰子 (SuperDice) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [PluginManager] Registered distributed tool: [云端] 等待用户回复 (WaitingForUrReply) from dist-mejanul7-852dvax
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.061Z - Registered 8 external tools from server dist-mejanul7-852dvax.
[2025/8/20 09:28:49] [LOG] [WebSocketServer] Received message from mejanul7-852dvax (DistributedServer): {"type":"report_ip","data":{"serverName":"VCP-Desktop-Client-Distributed-Server","localIPs":["*********"],"publicIP":null}}...
[2025/8/20 09:28:49] [LOG] [WebSocketServer] 2025-08-20T01:28:49.256Z - Received message from Distributed Server dist-mejanul7-852dvax: {"type":"report_ip","data":{"serverName":"VCP-Desktop-Client-Distributed-Server","localIPs":["*********"],"publicIP":null}}...
[2025/8/20 09:28:49] [LOG] [IP Tracker] Received IP report from Distributed Server 'VCP-Desktop-Client-Distributed-Server': Local IPs: [*********], Public IP: [N/A]
[2025/8/20 09:28:50] [LOG] [SynapsePusher] DEBUG: Current VCP_Key from config: "WEB_SOCKET_8xIUMi8S&^Os0%e8"
[2025/8/20 09:28:50] [LOG] [SynapsePusher] DEBUG: Current SERVER_PORT from config: "6005"
[2025/8/20 09:28:50] [LOG] [SynapsePusher] Attempting to connect to VCPLog WebSocket source at: ws://localhost:6005/VCPlog/VCP_Key=WEB_SOCKET_8xIUMi8S&^Os0%e8
[2025/8/20 09:28:50] [LOG] [WebSocketServer] 2025-08-20T01:28:50.894Z - VCPLog client attempting to connect.
[2025/8/20 09:28:50] [LOG] [WebSocketServer] 2025-08-20T01:28:50.894Z - Client mejanw0e-09s5o29 (Type: VCPLog) authenticated and connected.
[2025/8/20 09:28:50] [LOG] [WebSocketServer] Client mejanw0e-09s5o29 connected.
[2025/8/20 09:28:50] [LOG] [SynapsePusher] Successfully connected to VCPLog WebSocket source.
[2025/8/20 09:28:50] [LOG] [SynapsePusher] Received message from VCPLog WebSocket source: {
  "type": "connection_ack",
  "message": "WebSocket connection successful for VCPLog."
}
[2025/8/20 09:28:50] [LOG] [SynapsePusher] Connection acknowledgement from VCPLog source: WebSocket connection successful for VCPLog.
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] req.params.pathSegmentWithKey: 'pw=FILE_KEY_Qj!dHB0uz^Yuk0@Q'
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] Key comparison result: false
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] Authentication failed: Invalid key.
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] req.params.pathSegmentWithKey: 'pw=FILE_KEY_Qj!dHB0uz^Yuk0@Q'
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] Key comparison result: false
[2025/8/20 09:29:40] [LOG] [ImageAuthMiddleware] Authentication failed: Invalid key.
[2025/8/20 09:30:04] [LOG] [IP Tracker] Received POST request from IP: *********
[2025/8/20 09:30:04] [LOG] [IP Tracker] SUCCESS: Post request is from known Distributed Server: 'VCP-Desktop-Client-Distributed-Server' (IP: *********)
[2025/8/20 09:30:04] [LOG] [DebugLog] 已记录日志: LogInput-20250820_093004_633.txt
[2025/8/20 09:30:04] [LOG] [Server] Media processing enabled, calling ImageProcessor plugin...
[2025/8/20 09:30:04] [LOG] [Server] Calling message preprocessor: VCPTavern
[2025/8/20 09:30:04] [LOG] [DebugLog] 已记录日志: LogOutputAfterProcessing-20250820_093004_655.txt
[2025/8/20 09:30:08] [LOG] [VCP Stream Loop] Processing initial AI call.
[2025/8/20 09:30:10] [LOG] [VCP Stream Loop] Initial AI content (first 200): 好的，小M！我理解你希望预订下周三去深圳的机票和酒店。

我再次确认一下，今天是2025年8月20日星期三，所以你说的**下周三是2025年8月27日**，对吗？出发城市是上海吗？

很抱歉再次告知你，目前我**无法直接帮你预订机票和酒店**，因为我没有连接到这类预订系统的权限。

而且，我尝试联系其他Agent的工具 (`AgentAssistant`) 和联网搜索工具 (`TavilySear
[2025/8/20 09:30:10] [LOG] [VCP Stream Loop] No tool calls found in AI response. Sending final signals and exiting loop.
[2025/8/20 09:31:33] [LOG] Initiating graceful shutdown...
[2025/8/20 09:31:33] [LOG] [Server] Shutting down WebSocketServer...
[2025/8/20 09:31:33] [LOG] [WebSocketServer] Shutting down...
[2025/8/20 09:31:33] [LOG] [WebSocketServer] 2025-08-20T01:31:33.498Z - WebSocketServer shutdown.
[2025/8/20 09:31:33] [LOG] [PluginManager] Shutting down all plugins...
[2025/8/20 09:31:33] [LOG] [WebSocketServer] Client mejanul9-vn7blht (VCPLog) disconnected.
[2025/8/20 09:31:33] [LOG] [PluginManager] Unregistered 8 tools from server dist-mejanul7-852dvax.
[2025/8/20 09:31:33] [LOG] [WebSocketServer] 2025-08-20T01:31:33.502Z - Distributed Server dist-mejanul7-852dvax disconnected. Its tools and IP info have been unregistered.
[2025/8/20 09:31:33] [LOG] [WebSocketServer] Client mejanul7-852dvax (DistributedServer) disconnected.
[2025/8/20 09:31:33] [LOG] [WebSocketServer] Client mejanw0e-09s5o29 (VCPLog) disconnected.
[2025/8/20 09:31:33] [LOG] [WebSocketServer] Server closed.
[2025/8/20 09:31:33] [WARN] [SynapsePusher] WebSocket client to VCPLog source disconnected. Code: 1005, Reason: . Attempting to reconnect...
[2025/8/20 09:31:33] [LOG] [MultiModalProcessor][Debug] Media cache saved to D:\VCP\VCPToolBox\Plugin\ImageProcessor\multimodal_cache.json 
[2025/8/20 09:31:33] [LOG] [MultiModalProcessor] Shutdown complete, cache saved.
[2025/8/20 09:31:33] [LOG] [VCPTavern] 插件已卸载。
[2025/8/20 09:31:33] [LOG] [ChromeObserver] Shutting down.
[2025/8/20 09:31:33] [LOG] [SynapsePusher] Shutting down...
[2025/8/20 09:31:33] [LOG] [SynapsePusher] Shutdown complete.
[2025/8/20 09:31:33] [LOG] [VCPLog] Shutting down VCPLog plugin (logging only)...
[2025/8/20 09:31:33] [LOG] [VCPTavern] 插件已卸载。
[2025/8/20 09:31:33] [LOG] [PluginManager] All plugin shutdown processes initiated and scheduled jobs cancelled.
[2025/8/20 09:31:33] [LOG] [Server] Closing server log file stream...
[2025/8/20 09:31:33] Server gracefully shut down.
