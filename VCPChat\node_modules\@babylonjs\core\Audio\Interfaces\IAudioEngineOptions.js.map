{"version": 3, "file": "IAudioEngineOptions.js", "sourceRoot": "", "sources": ["../../../../../lts/core/generated/Audio/Interfaces/IAudioEngineOptions.ts"], "names": [], "mappings": "", "sourcesContent": ["/**\r\n * Interface used to define options for the Audio Engine\r\n * @since 5.0.0\r\n */\r\nexport interface IAudioEngineOptions {\r\n    /**\r\n     * Specifies an existing Audio Context for the audio engine\r\n     */\r\n    audioContext?: AudioContext;\r\n    /**\r\n     * Specifies a destination node for the audio engine\r\n     */\r\n    audioDestination?: AudioDestinationNode | MediaStreamAudioDestinationNode;\r\n}\r\n"]}