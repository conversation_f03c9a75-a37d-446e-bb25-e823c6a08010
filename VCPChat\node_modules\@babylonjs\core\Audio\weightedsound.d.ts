import type { Sound } from "../Audio/sound";
/**
 * Wraps one or more Sound objects and selects one with random weight for playback.
 */
export declare class WeightedSound {
    /** When true a Sound will be selected and played when the current playing Sound completes. */
    loop: boolean;
    private _coneInnerAngle;
    private _coneOuterAngle;
    private _volume;
    /** A Sound is currently playing. */
    isPlaying: boolean;
    /** A Sound is currently paused. */
    isPaused: boolean;
    private _sounds;
    private _weights;
    private _currentIndex?;
    /**
     * Creates a new WeightedSound from the list of sounds given.
     * @param loop When true a Sound will be selected and played when the current playing Sound completes.
     * @param sounds Array of Sounds that will be selected from.
     * @param weights Array of number values for selection weights; length must equal sounds, values will be normalized to 1
     */
    constructor(loop: boolean, sounds: Sound[], weights: number[]);
    /**
     * The size of cone in degrees for a directional sound in which there will be no attenuation.
     */
    get directionalConeInnerAngle(): number;
    /**
     * The size of cone in degrees for a directional sound in which there will be no attenuation.
     */
    set directionalConeInnerAngle(value: number);
    /**
     * Size of cone in degrees for a directional sound outside of which there will be no sound.
     * Listener angles between innerAngle and outerAngle will falloff linearly.
     */
    get directionalConeOuterAngle(): number;
    /**
     * Size of cone in degrees for a directional sound outside of which there will be no sound.
     * Listener angles between innerAngle and outerAngle will falloff linearly.
     */
    set directionalConeOuterAngle(value: number);
    /**
     * Playback volume.
     */
    get volume(): number;
    /**
     * Playback volume.
     */
    set volume(value: number);
    private _onended;
    /**
     * Suspend playback
     */
    pause(): void;
    /**
     * Stop playback
     */
    stop(): void;
    /**
     * Start playback.
     * @param startOffset Position the clip head at a specific time in seconds.
     */
    play(startOffset?: number): void;
}
