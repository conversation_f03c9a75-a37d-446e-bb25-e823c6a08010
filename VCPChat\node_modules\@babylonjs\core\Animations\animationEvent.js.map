{"version": 3, "file": "animationEvent.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/animationEvent.ts"], "names": [], "mappings": "AAAA;;GAEG;AACH,MAAM,OAAO,cAAc;IAMvB;;;;;OAKG;IACH;IACI,kDAAkD;IAC3C,KAAa;IACpB,2CAA2C;IACpC,MAAsC;IAC7C,2DAA2D;IACpD,QAAkB;QAJlB,UAAK,GAAL,KAAK,CAAQ;QAEb,WAAM,GAAN,MAAM,CAAgC;QAEtC,aAAQ,GAAR,QAAQ,CAAU;QAjB7B;;WAEG;QACI,WAAM,GAAY,KAAK,CAAC;IAe5B,CAAC;IAEJ,gBAAgB;IACT,MAAM;QACT,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtE,CAAC;CACJ", "sourcesContent": ["/**\r\n * Composed of a frame, and an action function\r\n */\r\nexport class AnimationEvent {\r\n    /**\r\n     * Specifies if the animation event is done\r\n     */\r\n    public isDone: boolean = false;\r\n\r\n    /**\r\n     * Initializes the animation event\r\n     * @param frame The frame for which the event is triggered\r\n     * @param action The event to perform when triggered\r\n     * @param onlyOnce Specifies if the event should be triggered only once\r\n     */\r\n    constructor(\r\n        /** The frame for which the event is triggered **/\r\n        public frame: number,\r\n        /** The event to perform when triggered **/\r\n        public action: (currentFrame: number) => void,\r\n        /** Specifies if the event should be triggered only once**/\r\n        public onlyOnce?: boolean\r\n    ) {}\r\n\r\n    /** @internal */\r\n    public _clone(): AnimationEvent {\r\n        return new AnimationEvent(this.frame, this.action, this.onlyOnce);\r\n    }\r\n}\r\n"]}