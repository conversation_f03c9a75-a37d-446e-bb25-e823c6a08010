{"version": 3, "file": "runtimeAnimation.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Animations/runtimeAnimation.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC5E,OAAO,EAAE,MAAM,EAAE,MAAM,qBAAqB,CAAC;AAE7C,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAOxC,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,8CAA8C;AAE9C,aAAa;AACb,MAAM,4BAA4B,GAA8B,MAAM,CAAC,MAAM,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAE1G,UAAU;AACV,MAAM,yBAAyB,GAA2B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExF,UAAU;AACV,MAAM,yBAAyB,GAA2B,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExF,OAAO;AACP,MAAM,sBAAsB,GAAwB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAE/E,SAAS;AACT,MAAM,wBAAwB,GAA0B,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAEtF;;GAEG;AACH,MAAM,OAAO,gBAAgB;IA2GzB;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB,CAAC;IAED;;OAEG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,IAAW,MAAM;QACb,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,IAAW,UAAU;QACjB,OAAO,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;IAC/C,CAAC;IAKD;;;;;;OAMG;IACH,YAAmB,MAAW,EAAE,SAAoB,EAAE,KAAY,EAAE,IAAgB;QA9J5E,YAAO,GAAG,IAAI,KAAK,EAAkB,CAAC;QAE9C;;WAEG;QACK,kBAAa,GAAW,CAAC,CAAC;QAiBlC;;WAEG;QACK,mBAAc,GAAG,IAAI,KAAK,EAAO,CAAC;QAE1C;;WAEG;QACK,wBAAmB,GAAkB,IAAI,CAAC;QAElD;;WAEG;QACK,kBAAa,GAA2B,EAAE,CAAC;QAEnD;;WAEG;QACK,qBAAgB,GAA2B,EAAE,CAAC;QAEtD;;WAEG;QACK,aAAQ,GAAG,KAAK,CAAC;QAEzB;;WAEG;QACK,oBAAe,GAAG,CAAC,CAAC;QAO5B;;WAEG;QACK,kBAAa,GAAkB,IAAI,CAAC;QASpC,yBAAoB,GAAkB,IAAI,CAAC;QAC3C,kBAAa,GAAkB,IAAI,CAAC;QAE5C;;WAEG;QACK,gBAAW,GAAW,EAAE,CAAC;QAEjC;;WAEG;QACK,YAAO,GAAG,GAAG,CAAC;QAEtB;;WAEG;QACK,iBAAY,GAAG,CAAC,CAAC;QAEzB;;WAEG;QACK,mBAAc,GAAW,CAAC,CAAC;QAEnC;;WAEG;QACK,mBAAc,GAAW,CAAC,CAAC;QAS3B,mBAAc,GAAG,KAAK,CAAC;QAuD3B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QAEzB,SAAS,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAExC,QAAQ;QACR,IAAI,CAAC,eAAe,GAAG;YACnB,GAAG,EAAE,CAAC;YACN,WAAW,EAAE,CAAC;YACd,QAAQ,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACvC,CAAC;QAEF,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,KAAK,SAAS,CAAC,oBAAoB,EAAE;YAC7D,IAAI,CAAC,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;SAClD;QAED,SAAS;QACT,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QACzD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;QAEzD,wCAAwC;QACxC,IAAI,IAAI,CAAC,SAAS,KAAK,CAAC,EAAE;YACtB,MAAM,MAAM,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;SACnC;QAED,aAAa;QACb,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE;YAC/B,IAAI,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;gBAC/B,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;gBACjC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;gBAC/B,KAAK,EAAE,CAAC;aACX;YACD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;SAC9B;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAChC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;YAC5B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SAC/C;QAED,yBAAyB;QACzB,MAAM,MAAM,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;QACrC,IAAI,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACjB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;SACN;QAED,IAAI,CAAC,eAAe,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;IAC7J,CAAC;IAEO,YAAY,CAAC,MAAW,EAAE,WAAW,GAAG,CAAC;QAC7C,MAAM,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,kBAAkB,CAAC;QAE9D,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YAC/B,IAAI,QAAQ,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;YAE7C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE,KAAK,EAAE,EAAE;gBAChE,QAAQ,GAAG,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;aAClD;YAED,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,QAAQ,CAAC;SAC/C;aAAM;YACH,IAAI,CAAC,WAAW,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;YACzC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC;SAC7C;IACL,CAAC;IAED;;OAEG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,eAAe,GAAG,KAAK;QAChC,IAAI,eAAe,EAAE;YACjB,IAAI,IAAI,CAAC,OAAO,YAAY,KAAK,EAAE;gBAC/B,IAAI,KAAK,GAAG,CAAC,CAAC;gBACd,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;oBAC/B,IAAI,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,SAAS,EAAE;wBAC1C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;qBAC7F;oBACD,KAAK,EAAE,CAAC;iBACX;aACJ;iBAAM;gBACH,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;oBACtC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;iBACnF;aACJ;SACJ;QAED,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;QACvB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;QAEzB,SAAS;QACT,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACtD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;SACtC;IACL,CAAC;IAED;;;OAGG;IACI,SAAS;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;YACZ,IAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,YAAiB,EAAE,MAAc;QAC7C,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBACtD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACnC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;aACnF;YACD,OAAO;SACV;QACD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC;IAEO,kBAAkB,CAAC,WAAW,GAAG,CAAC;QACtC,IAAI,aAAkB,CAAC;QACvB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEhD,IAAI,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;YACtD,YAAY;YACZ,aAAa,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;SACxC;aAAM;YACH,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;SAC5C;QAED,IAAI,aAAa,IAAI,aAAa,CAAC,KAAK,EAAE;YACtC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;SAC5D;aAAM;YACH,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC;SACpD;IACL,CAAC;IAEO,SAAS,CAAC,MAAW,EAAE,WAAgB,EAAE,YAAiB,EAAE,MAAc,EAAE,WAAmB;QACnG,YAAY;QACZ,IAAI,CAAC,oBAAoB,GAAG,WAAW,CAAC;QAExC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,IAAI,GAAG,EAAE;YACrD,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC3B,MAAM,aAAa,GAAG,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAEpD,IAAI,aAAa,CAAC,KAAK,EAAE;oBACrB,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;iBACpD;qBAAM;oBACH,IAAI,CAAC,mBAAmB,GAAG,aAAa,CAAC;iBAC5C;aACJ;YAED,IAAI,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE;gBAC5B,SAAS;gBACT,IAAI,SAAS,CAAC,oCAAoC,EAAE;oBAChD,IAAI,IAAI,CAAC,aAAa,EAAE;wBACpB,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;qBAC/G;yBAAM;wBACH,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;qBAC3G;iBACJ;qBAAM;oBACH,IAAI,IAAI,CAAC,aAAa,EAAE;wBACpB,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;qBACtG;yBAAM;wBACH,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;qBAClG;iBACJ;aACJ;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;aAC/G;YAED,MAAM,aAAa,GAAG,MAAM,IAAI,MAAM,CAAC,2BAA2B,CAAC,CAAC,CAAC,MAAM,CAAC,2BAA2B,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;YACtJ,IAAI,CAAC,eAAe,IAAI,aAAa,CAAC;SACzC;aAAM;YACH,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,IAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,KAAK,EAAE;oBACrB,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,EAAE,CAAC;iBAC7C;qBAAM;oBACH,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;iBACrC;aACJ;iBAAM,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;gBACpC,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;aAC7C;iBAAM;gBACH,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;aACrC;SACJ;QAED,IAAI,MAAM,KAAK,CAAC,GAAG,EAAE;YACjB,IAAI,CAAC,MAAM,CAAC,sCAAsC,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC,CAAC;SAC9F;aAAM;YACH,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC;SACtD;QAED,IAAI,MAAM,CAAC,WAAW,EAAE;YACpB,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;SACtD;IACL,CAAC;IAED;;;OAGG;IACK,mBAAmB;QACvB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,2BAA2B,EAAE;YAC1D,OAAO,IAAI,CAAC,OAAO,CAAC,2BAA2B,CAAC,QAAQ,CAAC;SAC5D;QAED,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IACpC,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,KAAa;QAC1B,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;QAEvC,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;YACvB,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;SACzB;aAAM,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE;YAC5C,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;SACvC;QAED,iCAAiC;QACjC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAC5B,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;oBACzB,6BAA6B;oBAC7B,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC;iBACtD;aACJ;SACJ;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAE/E,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACI,2BAA2B,CAAC,aAAqB;QACpD,MAAM,QAAQ,GAAG,CAAC,IAAI,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,aAAa,CAAC,CAAC,GAAG,MAAM,CAAC;QAEnG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;IACvD,CAAC;IAED;;;;;;;;;OASG;IACI,OAAO,CAAC,KAAa,EAAE,IAAY,EAAE,EAAU,EAAE,IAAa,EAAE,UAAkB,EAAE,MAAM,GAAG,CAAC,GAAG;QACpG,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,MAAM,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,kBAAkB,IAAI,kBAAkB,CAAC,MAAM,GAAG,CAAC,EAAE;YACtD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,KAAK,CAAC;SAChB;QAED,IAAI,WAAW,GAAG,IAAI,CAAC;QAEvB,eAAe;QACf,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,EAAE;YAChD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC;SACzB;QACD,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE;YAC5C,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;SACvB;QAED,MAAM,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC;QACxB,IAAI,WAAgB,CAAC;QAErB,qEAAqE;QACrE,MAAM,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,CAAC,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC;QAC7F,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAE5B,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE;YACvC,4DAA4D;YAC5D,WAAW,GAAG,KAAK,CAAC;YACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3D;aAAM,IAAI,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI,KAAK,IAAI,KAAK,EAAE;YAC9C,WAAW,GAAG,KAAK,CAAC;YACpB,cAAc,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC3D;aAAM,IAAI,IAAI,CAAC,eAAe,CAAC,QAAQ,KAAK,SAAS,CAAC,uBAAuB,EAAE;YAC5E,MAAM,SAAS,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;gBAChC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,CAAC,CAAC;gBACrC,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,SAAS,CAAC,uBAAuB,CAAC;gBAClE,MAAM,SAAS,GAAG,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBACrE,MAAM,OAAO,GAAG,SAAS,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;gBAEjE,IAAI,CAAC,eAAe,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3D,QAAQ,SAAS,CAAC,QAAQ,EAAE;oBACxB,QAAQ;oBACR,KAAK,SAAS,CAAC,mBAAmB;wBAC9B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,GAAG,SAAS,CAAC;wBACpD,MAAM;oBACV,aAAa;oBACb,KAAK,SAAS,CAAC,wBAAwB;wBACnC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM;oBACV,UAAU;oBACV,KAAK,SAAS,CAAC,qBAAqB;wBAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM;oBACV,UAAU;oBACV,KAAK,SAAS,CAAC,qBAAqB;wBAChC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM;oBACV,OAAO;oBACP,KAAK,SAAS,CAAC,kBAAkB;wBAC7B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM;oBACV,SAAS;oBACT,KAAK,SAAS,CAAC,oBAAoB;wBAC/B,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;wBAC5D,MAAM;oBACV;wBACI,MAAM;iBACb;gBAED,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;aAC9C;YAED,cAAc,GAAG,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAClD,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;SAC/C;QAED,IAAI,WAAW,KAAK,SAAS,EAAE;YAC3B,QAAQ,SAAS,CAAC,QAAQ,EAAE;gBACxB,QAAQ;gBACR,KAAK,SAAS,CAAC,mBAAmB;oBAC9B,WAAW,GAAG,CAAC,CAAC;oBAChB,MAAM;gBACV,aAAa;gBACb,KAAK,SAAS,CAAC,wBAAwB;oBACnC,WAAW,GAAG,4BAA4B,CAAC;oBAC3C,MAAM;gBACV,UAAU;gBACV,KAAK,SAAS,CAAC,qBAAqB;oBAChC,WAAW,GAAG,yBAAyB,CAAC;oBACxC,MAAM;gBACV,UAAU;gBACV,KAAK,SAAS,CAAC,qBAAqB;oBAChC,WAAW,GAAG,yBAAyB,CAAC;oBACxC,MAAM;gBACV,OAAO;gBACP,KAAK,SAAS,CAAC,kBAAkB;oBAC7B,WAAW,GAAG,sBAAsB,CAAC;oBACrC,MAAM;gBACV,SAAS;gBACT,KAAK,SAAS,CAAC,oBAAoB;oBAC/B,WAAW,GAAG,wBAAwB,CAAC;aAC9C;SACJ;QAED,gBAAgB;QAChB,IAAI,YAAoB,CAAC;QAEzB,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACnC,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC;YACrC,MAAM,mBAAmB,GAAG,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;YAClH,YAAY,GAAG,IAAI,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,mBAAmB,CAAC;SAC3D;aAAM;YACH,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE;gBACtD,YAAY,GAAG,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;aAC3E;iBAAM;gBACH,YAAY,GAAG,WAAW,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;aAC3E;SACJ;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;QAE5B,+BAA+B;QAC/B,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,EAAE;YAC9G,IAAI,CAAC,OAAO,EAAE,CAAC;YAEf,iCAAiC;YACjC,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChD,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE;oBACzB,wCAAwC;oBACxC,MAAM,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;iBAChC;aACJ;YAED,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC;SAClF;QACD,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;QAClC,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;QAC1E,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,cAAc,CAAC;QACrD,IAAI,CAAC,eAAe,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/C,MAAM,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC,YAAY,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QAEhF,YAAY;QACZ,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAEpC,eAAe;QACf,IAAI,MAAM,CAAC,MAAM,EAAE;YACf,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChD,kGAAkG;gBAClG,mDAAmD;gBACnD,IACI,CAAC,KAAK,GAAG,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC;oBACjF,CAAC,KAAK,GAAG,CAAC,IAAI,YAAY,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,EACnF;oBACE,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;oBAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;wBACf,gDAAgD;wBAChD,IAAI,KAAK,CAAC,QAAQ,EAAE;4BAChB,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;4BACxB,KAAK,EAAE,CAAC;yBACX;wBACD,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;wBACpB,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;qBAC9B,CAAC,sDAAsD;iBAC3D;aACJ;SACJ;QAED,IAAI,CAAC,WAAW,EAAE;YACd,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACxB;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;CACJ", "sourcesContent": ["import type { DeepImmutable, Nullable } from \"../types\";\r\nimport { Quaternion, Vector3, Vector2, Matrix } from \"../Maths/math.vector\";\r\nimport { Color3 } from \"../Maths/math.color\";\r\nimport type { _IAnimationState } from \"./animation\";\r\nimport { Animation } from \"./animation\";\r\nimport type { AnimationEvent } from \"./animationEvent\";\r\n\r\ndeclare type Animatable = import(\"./animatable\").Animatable;\r\n\r\nimport type { Scene } from \"../scene\";\r\nimport type { IAnimationKey } from \"./animationKey\";\r\nimport { Size } from \"../Maths/math.size\";\r\n\r\n// Static values to help the garbage collector\r\n\r\n// Quaternion\r\nconst _staticOffsetValueQuaternion: DeepImmutable<Quaternion> = Object.freeze(new Quaternion(0, 0, 0, 0));\r\n\r\n// Vector3\r\nconst _staticOffsetValueVector3: DeepImmutable<Vector3> = Object.freeze(Vector3.Zero());\r\n\r\n// Vector2\r\nconst _staticOffsetValueVector2: DeepImmutable<Vector2> = Object.freeze(Vector2.Zero());\r\n\r\n// Size\r\nconst _staticOffsetValueSize: DeepImmutable<Size> = Object.freeze(Size.Zero());\r\n\r\n// Color3\r\nconst _staticOffsetValueColor3: DeepImmutable<Color3> = Object.freeze(Color3.Black());\r\n\r\n/**\r\n * Defines a runtime animation\r\n */\r\nexport class RuntimeAnimation {\r\n    private _events = new Array<AnimationEvent>();\r\n\r\n    /**\r\n     * The current frame of the runtime animation\r\n     */\r\n    private _currentFrame: number = 0;\r\n\r\n    /**\r\n     * The animation used by the runtime animation\r\n     */\r\n    private _animation: Animation;\r\n\r\n    /**\r\n     * The target of the runtime animation\r\n     */\r\n    private _target: any;\r\n\r\n    /**\r\n     * The initiating animatable\r\n     */\r\n    private _host: Animatable;\r\n\r\n    /**\r\n     * The original value of the runtime animation\r\n     */\r\n    private _originalValue = new Array<any>();\r\n\r\n    /**\r\n     * The original blend value of the runtime animation\r\n     */\r\n    private _originalBlendValue: Nullable<any> = null;\r\n\r\n    /**\r\n     * The offsets cache of the runtime animation\r\n     */\r\n    private _offsetsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * The high limits cache of the runtime animation\r\n     */\r\n    private _highLimitsCache: { [key: string]: any } = {};\r\n\r\n    /**\r\n     * Specifies if the runtime animation has been stopped\r\n     */\r\n    private _stopped = false;\r\n\r\n    /**\r\n     * The blending factor of the runtime animation\r\n     */\r\n    private _blendingFactor = 0;\r\n\r\n    /**\r\n     * The BabylonJS scene\r\n     */\r\n    private _scene: Scene;\r\n\r\n    /**\r\n     * The current value of the runtime animation\r\n     */\r\n    private _currentValue: Nullable<any> = null;\r\n\r\n    /** @internal */\r\n    public _animationState: _IAnimationState;\r\n\r\n    /**\r\n     * The active target of the runtime animation\r\n     */\r\n    private _activeTargets: any[];\r\n    private _currentActiveTarget: Nullable<any> = null;\r\n    private _directTarget: Nullable<any> = null;\r\n\r\n    /**\r\n     * The target path of the runtime animation\r\n     */\r\n    private _targetPath: string = \"\";\r\n\r\n    /**\r\n     * The weight of the runtime animation\r\n     */\r\n    private _weight = 1.0;\r\n\r\n    /**\r\n     * The ratio offset of the runtime animation\r\n     */\r\n    private _ratioOffset = 0;\r\n\r\n    /**\r\n     * The previous delay of the runtime animation\r\n     */\r\n    private _previousDelay: number = 0;\r\n\r\n    /**\r\n     * The previous ratio of the runtime animation\r\n     */\r\n    private _previousRatio: number = 0;\r\n\r\n    private _enableBlending: boolean;\r\n\r\n    private _keys: IAnimationKey[];\r\n    private _minFrame: number;\r\n    private _maxFrame: number;\r\n    private _minValue: any;\r\n    private _maxValue: any;\r\n    private _targetIsArray = false;\r\n\r\n    /**\r\n     * Gets the current frame of the runtime animation\r\n     */\r\n    public get currentFrame(): number {\r\n        return this._currentFrame;\r\n    }\r\n\r\n    /**\r\n     * Gets the weight of the runtime animation\r\n     */\r\n    public get weight(): number {\r\n        return this._weight;\r\n    }\r\n\r\n    /**\r\n     * Gets the current value of the runtime animation\r\n     */\r\n    public get currentValue(): any {\r\n        return this._currentValue;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets the target path of the runtime animation\r\n     */\r\n    public get targetPath(): string {\r\n        return this._targetPath;\r\n    }\r\n\r\n    /**\r\n     * Gets the actual target of the runtime animation\r\n     */\r\n    public get target(): any {\r\n        return this._currentActiveTarget;\r\n    }\r\n\r\n    /**\r\n     * Gets the additive state of the runtime animation\r\n     */\r\n    public get isAdditive(): boolean {\r\n        return this._host && this._host.isAdditive;\r\n    }\r\n\r\n    /** @internal */\r\n    public _onLoop: () => void;\r\n\r\n    /**\r\n     * Create a new RuntimeAnimation object\r\n     * @param target defines the target of the animation\r\n     * @param animation defines the source animation object\r\n     * @param scene defines the hosting scene\r\n     * @param host defines the initiating Animatable\r\n     */\r\n    public constructor(target: any, animation: Animation, scene: Scene, host: Animatable) {\r\n        this._animation = animation;\r\n        this._target = target;\r\n        this._scene = scene;\r\n        this._host = host;\r\n        this._activeTargets = [];\r\n\r\n        animation._runtimeAnimations.push(this);\r\n\r\n        // State\r\n        this._animationState = {\r\n            key: 0,\r\n            repeatCount: 0,\r\n            loopMode: this._getCorrectLoopMode(),\r\n        };\r\n\r\n        if (this._animation.dataType === Animation.ANIMATIONTYPE_MATRIX) {\r\n            this._animationState.workValue = Matrix.Zero();\r\n        }\r\n\r\n        // Limits\r\n        this._keys = this._animation.getKeys();\r\n        this._minFrame = this._keys[0].frame;\r\n        this._maxFrame = this._keys[this._keys.length - 1].frame;\r\n        this._minValue = this._keys[0].value;\r\n        this._maxValue = this._keys[this._keys.length - 1].value;\r\n\r\n        // Add a start key at frame 0 if missing\r\n        if (this._minFrame !== 0) {\r\n            const newKey = { frame: 0, value: this._minValue };\r\n            this._keys.splice(0, 0, newKey);\r\n        }\r\n\r\n        // Check data\r\n        if (this._target instanceof Array) {\r\n            let index = 0;\r\n            for (const target of this._target) {\r\n                this._preparePath(target, index);\r\n                this._getOriginalValues(index);\r\n                index++;\r\n            }\r\n            this._targetIsArray = true;\r\n        } else {\r\n            this._preparePath(this._target);\r\n            this._getOriginalValues();\r\n            this._targetIsArray = false;\r\n            this._directTarget = this._activeTargets[0];\r\n        }\r\n\r\n        // Cloning events locally\r\n        const events = animation.getEvents();\r\n        if (events && events.length > 0) {\r\n            events.forEach((e) => {\r\n                this._events.push(e._clone());\r\n            });\r\n        }\r\n\r\n        this._enableBlending = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.enableBlending : this._animation.enableBlending;\r\n    }\r\n\r\n    private _preparePath(target: any, targetIndex = 0) {\r\n        const targetPropertyPath = this._animation.targetPropertyPath;\r\n\r\n        if (targetPropertyPath.length > 1) {\r\n            let property = target[targetPropertyPath[0]];\r\n\r\n            for (let index = 1; index < targetPropertyPath.length - 1; index++) {\r\n                property = property[targetPropertyPath[index]];\r\n            }\r\n\r\n            this._targetPath = targetPropertyPath[targetPropertyPath.length - 1];\r\n            this._activeTargets[targetIndex] = property;\r\n        } else {\r\n            this._targetPath = targetPropertyPath[0];\r\n            this._activeTargets[targetIndex] = target;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the animation from the runtime animation\r\n     */\r\n    public get animation(): Animation {\r\n        return this._animation;\r\n    }\r\n\r\n    /**\r\n     * Resets the runtime animation to the beginning\r\n     * @param restoreOriginal defines whether to restore the target property to the original value\r\n     */\r\n    public reset(restoreOriginal = false): void {\r\n        if (restoreOriginal) {\r\n            if (this._target instanceof Array) {\r\n                let index = 0;\r\n                for (const target of this._target) {\r\n                    if (this._originalValue[index] !== undefined) {\r\n                        this._setValue(target, this._activeTargets[index], this._originalValue[index], -1, index);\r\n                    }\r\n                    index++;\r\n                }\r\n            } else {\r\n                if (this._originalValue[0] !== undefined) {\r\n                    this._setValue(this._target, this._directTarget, this._originalValue[0], -1, 0);\r\n                }\r\n            }\r\n        }\r\n\r\n        this._offsetsCache = {};\r\n        this._highLimitsCache = {};\r\n        this._currentFrame = 0;\r\n        this._blendingFactor = 0;\r\n\r\n        // Events\r\n        for (let index = 0; index < this._events.length; index++) {\r\n            this._events[index].isDone = false;\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Specifies if the runtime animation is stopped\r\n     * @returns Boolean specifying if the runtime animation is stopped\r\n     */\r\n    public isStopped(): boolean {\r\n        return this._stopped;\r\n    }\r\n\r\n    /**\r\n     * Disposes of the runtime animation\r\n     * Note: No hard dispose should happen here as this method is skipped for performance reason (look at animatable.stop())\r\n     */\r\n    public dispose(): void {\r\n        const index = this._animation.runtimeAnimations.indexOf(this);\r\n\r\n        if (index > -1) {\r\n            this._animation.runtimeAnimations.splice(index, 1);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Apply the interpolated value to the target\r\n     * @param currentValue defines the value computed by the animation\r\n     * @param weight defines the weight to apply to this value (Defaults to 1.0)\r\n     */\r\n    public setValue(currentValue: any, weight: number) {\r\n        if (this._targetIsArray) {\r\n            for (let index = 0; index < this._target.length; index++) {\r\n                const target = this._target[index];\r\n                this._setValue(target, this._activeTargets[index], currentValue, weight, index);\r\n            }\r\n            return;\r\n        }\r\n        this._setValue(this._target, this._directTarget, currentValue, weight, 0);\r\n    }\r\n\r\n    private _getOriginalValues(targetIndex = 0) {\r\n        let originalValue: any;\r\n        const target = this._activeTargets[targetIndex];\r\n\r\n        if (target.getRestPose && this._targetPath === \"_matrix\") {\r\n            // For bones\r\n            originalValue = target.getRestPose();\r\n        } else {\r\n            originalValue = target[this._targetPath];\r\n        }\r\n\r\n        if (originalValue && originalValue.clone) {\r\n            this._originalValue[targetIndex] = originalValue.clone();\r\n        } else {\r\n            this._originalValue[targetIndex] = originalValue;\r\n        }\r\n    }\r\n\r\n    private _setValue(target: any, destination: any, currentValue: any, weight: number, targetIndex: number): void {\r\n        // Set value\r\n        this._currentActiveTarget = destination;\r\n\r\n        this._weight = weight;\r\n\r\n        if (this._enableBlending && this._blendingFactor <= 1.0) {\r\n            if (!this._originalBlendValue) {\r\n                const originalValue = destination[this._targetPath];\r\n\r\n                if (originalValue.clone) {\r\n                    this._originalBlendValue = originalValue.clone();\r\n                } else {\r\n                    this._originalBlendValue = originalValue;\r\n                }\r\n            }\r\n\r\n            if (this._originalBlendValue.m) {\r\n                // Matrix\r\n                if (Animation.AllowMatrixDecomposeForInterpolation) {\r\n                    if (this._currentValue) {\r\n                        Matrix.DecomposeLerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.DecomposeLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                } else {\r\n                    if (this._currentValue) {\r\n                        Matrix.LerpToRef(this._originalBlendValue, currentValue, this._blendingFactor, this._currentValue);\r\n                    } else {\r\n                        this._currentValue = Matrix.Lerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n                    }\r\n                }\r\n            } else {\r\n                this._currentValue = Animation._UniversalLerp(this._originalBlendValue, currentValue, this._blendingFactor);\r\n            }\r\n\r\n            const blendingSpeed = target && target.animationPropertiesOverride ? target.animationPropertiesOverride.blendingSpeed : this._animation.blendingSpeed;\r\n            this._blendingFactor += blendingSpeed;\r\n        } else {\r\n            if (!this._currentValue) {\r\n                if (currentValue?.clone) {\r\n                    this._currentValue = currentValue.clone();\r\n                } else {\r\n                    this._currentValue = currentValue;\r\n                }\r\n            } else if (this._currentValue.copyFrom) {\r\n                this._currentValue.copyFrom(currentValue);\r\n            } else {\r\n                this._currentValue = currentValue;\r\n            }\r\n        }\r\n\r\n        if (weight !== -1.0) {\r\n            this._scene._registerTargetForLateAnimationBinding(this, this._originalValue[targetIndex]);\r\n        } else {\r\n            destination[this._targetPath] = this._currentValue;\r\n        }\r\n\r\n        if (target.markAsDirty) {\r\n            target.markAsDirty(this._animation.targetProperty);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Gets the loop pmode of the runtime animation\r\n     * @returns Loop Mode\r\n     */\r\n    private _getCorrectLoopMode(): number | undefined {\r\n        if (this._target && this._target.animationPropertiesOverride) {\r\n            return this._target.animationPropertiesOverride.loopMode;\r\n        }\r\n\r\n        return this._animation.loopMode;\r\n    }\r\n\r\n    /**\r\n     * Move the current animation to a given frame\r\n     * @param frame defines the frame to move to\r\n     */\r\n    public goToFrame(frame: number): void {\r\n        const keys = this._animation.getKeys();\r\n\r\n        if (frame < keys[0].frame) {\r\n            frame = keys[0].frame;\r\n        } else if (frame > keys[keys.length - 1].frame) {\r\n            frame = keys[keys.length - 1].frame;\r\n        }\r\n\r\n        // Need to reset animation events\r\n        const events = this._events;\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                if (!events[index].onlyOnce) {\r\n                    // reset events in the future\r\n                    events[index].isDone = events[index].frame < frame;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._currentFrame = frame;\r\n        const currentValue = this._animation._interpolate(frame, this._animationState);\r\n\r\n        this.setValue(currentValue, -1);\r\n    }\r\n\r\n    /**\r\n     * @internal Internal use only\r\n     */\r\n    public _prepareForSpeedRatioChange(newSpeedRatio: number): void {\r\n        const newRatio = (this._previousDelay * (this._animation.framePerSecond * newSpeedRatio)) / 1000.0;\r\n\r\n        this._ratioOffset = this._previousRatio - newRatio;\r\n    }\r\n\r\n    /**\r\n     * Execute the current animation\r\n     * @param delay defines the delay to add to the current frame\r\n     * @param from defines the lower bound of the animation range\r\n     * @param to defines the upper bound of the animation range\r\n     * @param loop defines if the current animation must loop\r\n     * @param speedRatio defines the current speed ratio\r\n     * @param weight defines the weight of the animation (default is -1 so no weight)\r\n     * @returns a boolean indicating if the animation is running\r\n     */\r\n    public animate(delay: number, from: number, to: number, loop: boolean, speedRatio: number, weight = -1.0): boolean {\r\n        const animation = this._animation;\r\n        const targetPropertyPath = animation.targetPropertyPath;\r\n        if (!targetPropertyPath || targetPropertyPath.length < 1) {\r\n            this._stopped = true;\r\n            return false;\r\n        }\r\n\r\n        let returnValue = true;\r\n\r\n        // Check limits\r\n        if (from < this._minFrame || from > this._maxFrame) {\r\n            from = this._minFrame;\r\n        }\r\n        if (to < this._minFrame || to > this._maxFrame) {\r\n            to = this._maxFrame;\r\n        }\r\n\r\n        const range = to - from;\r\n        let offsetValue: any;\r\n\r\n        // Compute ratio which represents the frame delta between from and to\r\n        const ratio = (delay * (animation.framePerSecond * speedRatio)) / 1000.0 + this._ratioOffset;\r\n        let highLimitValue = 0;\r\n\r\n        this._previousDelay = delay;\r\n        this._previousRatio = ratio;\r\n\r\n        if (!loop && to >= from && ratio >= range) {\r\n            // If we are out of range and not looping get back to caller\r\n            returnValue = false;\r\n            highLimitValue = animation._getKeyValue(this._maxValue);\r\n        } else if (!loop && from >= to && ratio <= range) {\r\n            returnValue = false;\r\n            highLimitValue = animation._getKeyValue(this._minValue);\r\n        } else if (this._animationState.loopMode !== Animation.ANIMATIONLOOPMODE_CYCLE) {\r\n            const keyOffset = to.toString() + from.toString();\r\n            if (!this._offsetsCache[keyOffset]) {\r\n                this._animationState.repeatCount = 0;\r\n                this._animationState.loopMode = Animation.ANIMATIONLOOPMODE_CYCLE;\r\n                const fromValue = animation._interpolate(from, this._animationState);\r\n                const toValue = animation._interpolate(to, this._animationState);\r\n\r\n                this._animationState.loopMode = this._getCorrectLoopMode();\r\n                switch (animation.dataType) {\r\n                    // Float\r\n                    case Animation.ANIMATIONTYPE_FLOAT:\r\n                        this._offsetsCache[keyOffset] = toValue - fromValue;\r\n                        break;\r\n                    // Quaternion\r\n                    case Animation.ANIMATIONTYPE_QUATERNION:\r\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                        break;\r\n                    // Vector3\r\n                    case Animation.ANIMATIONTYPE_VECTOR3:\r\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                        break;\r\n                    // Vector2\r\n                    case Animation.ANIMATIONTYPE_VECTOR2:\r\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                        break;\r\n                    // Size\r\n                    case Animation.ANIMATIONTYPE_SIZE:\r\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                        break;\r\n                    // Color3\r\n                    case Animation.ANIMATIONTYPE_COLOR3:\r\n                        this._offsetsCache[keyOffset] = toValue.subtract(fromValue);\r\n                        break;\r\n                    default:\r\n                        break;\r\n                }\r\n\r\n                this._highLimitsCache[keyOffset] = toValue;\r\n            }\r\n\r\n            highLimitValue = this._highLimitsCache[keyOffset];\r\n            offsetValue = this._offsetsCache[keyOffset];\r\n        }\r\n\r\n        if (offsetValue === undefined) {\r\n            switch (animation.dataType) {\r\n                // Float\r\n                case Animation.ANIMATIONTYPE_FLOAT:\r\n                    offsetValue = 0;\r\n                    break;\r\n                // Quaternion\r\n                case Animation.ANIMATIONTYPE_QUATERNION:\r\n                    offsetValue = _staticOffsetValueQuaternion;\r\n                    break;\r\n                // Vector3\r\n                case Animation.ANIMATIONTYPE_VECTOR3:\r\n                    offsetValue = _staticOffsetValueVector3;\r\n                    break;\r\n                // Vector2\r\n                case Animation.ANIMATIONTYPE_VECTOR2:\r\n                    offsetValue = _staticOffsetValueVector2;\r\n                    break;\r\n                // Size\r\n                case Animation.ANIMATIONTYPE_SIZE:\r\n                    offsetValue = _staticOffsetValueSize;\r\n                    break;\r\n                // Color3\r\n                case Animation.ANIMATIONTYPE_COLOR3:\r\n                    offsetValue = _staticOffsetValueColor3;\r\n            }\r\n        }\r\n\r\n        // Compute value\r\n        let currentFrame: number;\r\n\r\n        if (this._host && this._host.syncRoot) {\r\n            const syncRoot = this._host.syncRoot;\r\n            const hostNormalizedFrame = (syncRoot.masterFrame - syncRoot.fromFrame) / (syncRoot.toFrame - syncRoot.fromFrame);\r\n            currentFrame = from + (to - from) * hostNormalizedFrame;\r\n        } else {\r\n            if ((ratio > 0 && from > to) || (ratio < 0 && from < to)) {\r\n                currentFrame = returnValue && range !== 0 ? to + (ratio % range) : from;\r\n            } else {\r\n                currentFrame = returnValue && range !== 0 ? from + (ratio % range) : to;\r\n            }\r\n        }\r\n\r\n        const events = this._events;\r\n\r\n        // Reset event/state if looping\r\n        if ((speedRatio > 0 && this.currentFrame > currentFrame) || (speedRatio < 0 && this.currentFrame < currentFrame)) {\r\n            this._onLoop();\r\n\r\n            // Need to reset animation events\r\n            for (let index = 0; index < events.length; index++) {\r\n                if (!events[index].onlyOnce) {\r\n                    // reset event, the animation is looping\r\n                    events[index].isDone = false;\r\n                }\r\n            }\r\n\r\n            this._animationState.key = speedRatio > 0 ? 0 : animation.getKeys().length - 1;\r\n        }\r\n        this._currentFrame = currentFrame;\r\n        this._animationState.repeatCount = range === 0 ? 0 : (ratio / range) >> 0;\r\n        this._animationState.highLimitValue = highLimitValue;\r\n        this._animationState.offsetValue = offsetValue;\r\n\r\n        const currentValue = animation._interpolate(currentFrame, this._animationState);\r\n\r\n        // Set value\r\n        this.setValue(currentValue, weight);\r\n\r\n        // Check events\r\n        if (events.length) {\r\n            for (let index = 0; index < events.length; index++) {\r\n                // Make sure current frame has passed event frame and that event frame is within the current range\r\n                // Also, handle both forward and reverse animations\r\n                if (\r\n                    (range > 0 && currentFrame >= events[index].frame && events[index].frame >= from) ||\r\n                    (range < 0 && currentFrame <= events[index].frame && events[index].frame <= from)\r\n                ) {\r\n                    const event = events[index];\r\n                    if (!event.isDone) {\r\n                        // If event should be done only once, remove it.\r\n                        if (event.onlyOnce) {\r\n                            events.splice(index, 1);\r\n                            index--;\r\n                        }\r\n                        event.isDone = true;\r\n                        event.action(currentFrame);\r\n                    } // Don't do anything if the event has already be done.\r\n                }\r\n            }\r\n        }\r\n\r\n        if (!returnValue) {\r\n            this._stopped = true;\r\n        }\r\n\r\n        return returnValue;\r\n    }\r\n}\r\n"]}