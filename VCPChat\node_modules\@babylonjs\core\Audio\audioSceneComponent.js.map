{"version": 3, "file": "audioSceneComponent.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Audio/audioSceneComponent.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,KAAK,EAAE,MAAM,SAAS,CAAC;AAChC,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,mBAAmB,CAAC;AAE3C,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAEvD,OAAO,EAAE,uBAAuB,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,EAAE,KAAK,EAAE,MAAM,UAAU,CAAC;AACjC,OAAO,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGjD,OAAO,eAAe,CAAC;AACvB,OAAO,EAAE,aAAa,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,wBAAwB,CAAC;AAErD,wCAAwC;AACxC,aAAa,CAAC,SAAS,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC,UAAe,EAAE,KAAY,EAAE,SAAyB,EAAE,OAAe,EAAE,EAAE;;IACtI,kBAAkB;IAClB,IAAI,YAAY,GAAY,EAAE,CAAC;IAC/B,IAAI,WAAkB,CAAC;IACvB,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM,IAAI,EAAE,CAAC;IAC1C,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,IAAI,EAAE;QAC/D,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;YAC1E,MAAM,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,MAAA,MAAM,CAAC,WAAW,0CAAE,cAAc,EAAE;gBACpC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE;oBAClB,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC;iBACtC;gBACD,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;oBAChC,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;oBACvD,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;oBAC5C,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBACtC;qBAAM;oBACH,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;iBAClG;aACJ;iBAAM;gBACH,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;aACnE;SACJ;KACJ;IAED,YAAY,GAAG,EAAE,CAAC;AACtB,CAAC,CAAC,CAAC;AAmEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE;IACrD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,eAAe,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SACpE;QAED,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,KAAK,CAAC,SAAS,CAAC,cAAc,GAAG,UAAU,IAAY;IACnD,IAAI,KAAa,CAAC;IAClB,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;QACzE,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;YAC1D,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;SACrD;KACJ;IAED,IAAI,IAAI,CAAC,WAAW,EAAE;QAClB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;YAChE,KAAK,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAC/E,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;oBAChE,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;iBAC3D;aACJ;SACJ;KACJ;IAED,OAAO,IAAI,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,cAAc,EAAE;IACnD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,OAAO,KAAK,CAAC,YAAY,CAAC;IAC9B,CAAC;IACD,GAAG,EAAE,UAAuB,KAAc;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,WAAW,EAAE,CAAC;SACvB;aAAM;YACH,KAAK,CAAC,YAAY,EAAE,CAAC;SACxB;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE;IAChD,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,OAAO,KAAK,CAAC,SAAS,CAAC;IAC3B,CAAC;IACD,GAAG,EAAE,UAAuB,KAAc;QACtC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,KAAK,EAAE;YACP,KAAK,CAAC,4BAA4B,EAAE,CAAC;SACxC;aAAM;YACH,KAAK,CAAC,gCAAgC,EAAE,CAAC;SAC5C;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,+BAA+B,EAAE;IACpE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,OAAO,KAAK,CAAC,6BAA6B,CAAC;IAC/C,CAAC;IACD,GAAG,EAAE,UAAuB,KAAoB;QAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,qGAAqG,CAAC,CAAC;SAC1H;aAAM;YACH,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC;SAC/C;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,+BAA+B,EAAE;IACpE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,OAAO,KAAK,CAAC,6BAA6B,CAAC;IAC/C,CAAC;IACD,GAAG,EAAE,UAAuB,KAAoB;QAC5C,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,IAAI,OAAO,KAAK,KAAK,UAAU,EAAE;YAC7B,MAAM,IAAI,KAAK,CAAC,qGAAqG,CAAC,CAAC;SAC1H;aAAM;YACH,KAAK,CAAC,6BAA6B,GAAG,KAAK,CAAC;SAC/C;IACL,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,6BAA6B,EAAE;IAClE,GAAG,EAAE;QACD,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,OAAO,KAAK,CAAC,2BAA2B,CAAC;IAC7C,CAAC;IACD,GAAG,EAAE,UAAuB,KAAa;QACrC,IAAI,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAwB,CAAC;QAC1F,IAAI,CAAC,KAAK,EAAE;YACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;QAED,KAAK,CAAC,2BAA2B,GAAG,KAAK,CAAC;IAC9C,CAAC;IACD,UAAU,EAAE,IAAI;IAChB,YAAY,EAAE,IAAI;CACrB,CAAC,CAAC;AAEH;;;GAGG;AACH,MAAM,OAAO,mBAAmB;IAc5B;;;OAGG;IACH,IAAW,YAAY;QACnB,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAGD;;;OAGG;IACH,IAAW,SAAS;QAChB,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B,CAAC;IAmBD;;;OAGG;IACH,YAAY,KAAuB;QAjDnC;;WAEG;QACa,SAAI,GAAG,uBAAuB,CAAC,UAAU,CAAC;QAOlD,kBAAa,GAAG,IAAI,CAAC;QASrB,eAAU,GAAG,KAAK,CAAC;QAS3B;;WAEG;QACI,gCAA2B,GAAG,GAAG,CAAC;QAEzC;;;WAGG;QACI,kCAA6B,GAA4B,IAAI,CAAC;QAErE;;;WAGG;QACI,kCAA6B,GAA4B,IAAI,CAAC;QAuL7D,2BAAsB,GAAG,IAAI,OAAO,EAAE,CAAC;QACvC,0BAAqB,GAAG,IAAI,OAAO,EAAE,CAAC;QACtC,eAAU,GAAG,CAAC,CAAC;QACf,sBAAiB,GAAG,IAAI,MAAM,EAAE,CAAC;QACjC,yBAAoB,GAAG,IAAI,OAAO,EAAE,CAAC;QApLzC,KAAK,GAAG,KAAK,IAAI,WAAW,CAAC,gBAAgB,CAAC;QAC9C,IAAI,CAAC,KAAK,EAAE;YACR,OAAO;SACV;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,KAAK,CAAC,WAAW,GAAG,IAAI,KAAK,EAAc,CAAC;QAC5C,KAAK,CAAC,MAAM,GAAG,IAAI,KAAK,EAAS,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,QAAQ;QACX,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,YAAY,CAAC,uBAAuB,CAAC,sBAAsB,EAAE,IAAI,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;IACvH,CAAC;IAED;;;OAGG;IACI,OAAO;QACV,8CAA8C;IAClD,CAAC;IAED;;;OAGG;IACI,SAAS,CAAC,mBAAwB;QACrC,mBAAmB,CAAC,MAAM,GAAG,EAAE,CAAC;QAEhC,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;YACxB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;gBAChE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBAEjD,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;oBAC1E,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC;iBACpF;aACJ;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,SAAwB;QAC5C,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACnB,OAAO;SACV;QACD,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACI,mBAAmB,CAAC,SAAwB,EAAE,OAAO,GAAG,KAAK;QAChE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;YACnB,OAAO;SACV;QACD,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAC/B,KAAK,CAAC,IAAI,EAAE,CAAC;YACb,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;YACvB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAC7C,IAAI,OAAO,EAAE;gBACT,KAAK,CAAC,OAAO,EAAE,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,KAAK,CAAC,eAAe,EAAE;YACvB,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;SAClC;QAED,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;gBACjE,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,OAAO,EAAE,CAAC;aACxC;SACJ;IACL,CAAC;IAED;;OAEG;IACI,YAAY;QACf,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAE3B,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;YACvD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC;SAC7C;QAED,IAAI,CAAS,CAAC;QACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9D,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;SACnD;QACD,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClE,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;iBACnD;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,WAAW;QACd,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,WAAW,CAAC,YAAY,EAAE;YACvD,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;SAC5C;QAED,IAAI,CAAS,CAAC;QACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC9D,IAAI,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;gBAClD,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aAClD;SACJ;QACD,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAClE,IAAI,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;wBAClD,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;qBAClD;iBACJ;aACJ;SACJ;IACL,CAAC;IAED;;OAEG;IACI,4BAA4B;QAC/B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,KAAK,CAAC,cAAc,CAAC,wBAAwB,EAAE,CAAC;QAChD,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC;aACnD;SACJ;IACL,CAAC;IAED;;OAEG;IACI,gCAAgC;QACnC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;QAExB,KAAK,CAAC,cAAc,CAAC,8BAA8B,EAAE,CAAC;QAEtD,IAAI,KAAK,CAAC,WAAW,EAAE;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC/C,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,8BAA8B,EAAE,CAAC;aACzD;SACJ;IACL,CAAC;IAQO,YAAY;QAChB,MAAM,GAAG,GAAG,aAAa,CAAC,GAAG,CAAC;QAC9B,IAAI,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,2BAA2B,EAAE;YAC7E,OAAO;SACV;QAED,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC;QAEtB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,eAAe,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,KAAK,CAAC,CAAC,EAAE;YAC/J,OAAO;SACV;QAED,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC;QAEvC,IAAI,CAAC,WAAW,EAAE;YACd,OAAO;SACV;QAED,IAAI,WAAW,CAAC,YAAY,EAAE;YAC1B,IAAI,eAAe,GAAG,KAAK,CAAC,YAAY,CAAC;YACzC,IAAI,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;gBACvD,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;aAC5C;YAED,8CAA8C;YAC9C,sDAAsD;YACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACpC,MAAM,QAAQ,GAAY,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBAC/D,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACjG,uCAAuC;aAC1C;iBAAM,IAAI,eAAe,EAAE;gBACxB,oEAAoE;gBACpE,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE;oBACpE,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC;oBACpE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;iBACvJ;aACJ;YACD,iDAAiD;iBAC5C;gBACD,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aAC1D;YAED,8CAA8C;YAC9C,sDAAsD;YACtD,IAAI,IAAI,CAAC,6BAA6B,EAAE;gBACpC,MAAM,QAAQ,GAAY,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBAC/D,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7G,uCAAuC;aAC1C;iBAAM,IAAI,eAAe,EAAE;gBACxB,iBAAiB;gBACjB,IAAI,eAAe,CAAC,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;oBACrE,eAAe,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;iBACnD;gBAED,eAAe,CAAC,aAAa,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBACpE,OAAO,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;gBACtH,IAAI,CAAC,oBAAoB,CAAC,SAAS,EAAE,CAAC;gBACtC,iCAAiC;gBACjC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE;oBACnH,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC,EAAE;wBAChE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;wBAChE,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;qBACpJ;iBACJ;aACJ;YACD,iDAAiD;iBAC5C;gBACD,4BAA4B;gBAC5B,WAAW,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;aACtE;YAED,IAAI,CAAS,CAAC;YACd,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC9D,MAAM,KAAK,GAAG,KAAK,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;gBACtD,IAAI,KAAK,CAAC,oBAAoB,EAAE;oBAC5B,KAAK,CAAC,0BAA0B,EAAE,CAAC;iBACtC;aACJ;YACD,IAAI,KAAK,CAAC,WAAW,EAAE;gBACnB,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAClE,MAAM,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;wBACtD,IAAI,KAAK,CAAC,oBAAoB,EAAE;4BAC5B,KAAK,CAAC,0BAA0B,EAAE,CAAC;yBACtC;qBACJ;iBACJ;aACJ;SACJ;IACL,CAAC;;AAtUc,oCAAgB,GAAG,IAAI,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAyU5D,KAAK,CAAC,6BAA6B,GAAG,CAAC,KAAY,EAAE,EAAE;IACnD,IAAI,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,uBAAuB,CAAC,UAAU,CAAC,CAAC;IACpE,IAAI,CAAC,KAAK,EAAE;QACR,KAAK,GAAG,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACvC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KAC9B;AACL,CAAC,CAAC", "sourcesContent": ["import { Sound } from \"./sound\";\r\nimport { SoundTrack } from \"./soundTrack\";\r\nimport { Engine } from \"../Engines/engine\";\r\nimport type { Nullable } from \"../types\";\r\nimport { Matrix, Vector3 } from \"../Maths/math.vector\";\r\nimport type { ISceneSerializableComponent } from \"../sceneComponent\";\r\nimport { SceneComponentConstants } from \"../sceneComponent\";\r\nimport { Scene } from \"../scene\";\r\nimport { AbstractScene } from \"../abstractScene\";\r\nimport type { AssetContainer } from \"../assetContainer\";\r\n\r\nimport \"./audioEngine\";\r\nimport { PrecisionDate } from \"../Misc/precisionDate\";\r\nimport { EngineStore } from \"../Engines/engineStore\";\r\n\r\n// Adds the parser to the scene parsers.\r\nAbstractScene.AddParser(SceneComponentConstants.NAME_AUDIO, (parsedData: any, scene: Scene, container: AssetContainer, rootUrl: string) => {\r\n    // TODO: add sound\r\n    let loadedSounds: Sound[] = [];\r\n    let loadedSound: Sound;\r\n    container.sounds = container.sounds || [];\r\n    if (parsedData.sounds !== undefined && parsedData.sounds !== null) {\r\n        for (let index = 0, cache = parsedData.sounds.length; index < cache; index++) {\r\n            const parsedSound = parsedData.sounds[index];\r\n            if (Engine.audioEngine?.canUseWebAudio) {\r\n                if (!parsedSound.url) {\r\n                    parsedSound.url = parsedSound.name;\r\n                }\r\n                if (!loadedSounds[parsedSound.url]) {\r\n                    loadedSound = Sound.Parse(parsedSound, scene, rootUrl);\r\n                    loadedSounds[parsedSound.url] = loadedSound;\r\n                    container.sounds.push(loadedSound);\r\n                } else {\r\n                    container.sounds.push(Sound.Parse(parsedSound, scene, rootUrl, loadedSounds[parsedSound.url]));\r\n                }\r\n            } else {\r\n                container.sounds.push(new Sound(parsedSound.name, null, scene));\r\n            }\r\n        }\r\n    }\r\n\r\n    loadedSounds = [];\r\n});\r\n\r\ndeclare module \"../abstractScene\" {\r\n    export interface AbstractScene {\r\n        /**\r\n         * The list of sounds used in the scene.\r\n         */\r\n        sounds: Nullable<Array<Sound>>;\r\n    }\r\n}\r\n\r\ndeclare module \"../scene\" {\r\n    export interface Scene {\r\n        /**\r\n         * @internal\r\n         * Backing field\r\n         */\r\n        _mainSoundTrack: SoundTrack;\r\n        /**\r\n         * The main sound track played by the scene.\r\n         * It contains your primary collection of sounds.\r\n         */\r\n        mainSoundTrack: SoundTrack;\r\n        /**\r\n         * The list of sound tracks added to the scene\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        soundTracks: Nullable<Array<SoundTrack>>;\r\n\r\n        /**\r\n         * Gets a sound using a given name\r\n         * @param name defines the name to search for\r\n         * @returns the found sound or null if not found at all.\r\n         */\r\n        getSoundByName(name: string): Nullable<Sound>;\r\n\r\n        /**\r\n         * Gets or sets if audio support is enabled\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioEnabled: boolean;\r\n\r\n        /**\r\n         * Gets or sets if audio will be output to headphones\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        headphone: boolean;\r\n\r\n        /**\r\n         * Gets or sets custom audio listener position provider\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioListenerPositionProvider: Nullable<() => Vector3>;\r\n\r\n        /**\r\n         * Gets or sets custom audio listener rotation provider\r\n         * @see https://doc.babylonjs.com/features/featuresDeepDive/audio/playingSoundsMusic\r\n         */\r\n        audioListenerRotationProvider: Nullable<() => Vector3>;\r\n\r\n        /**\r\n         * Gets or sets a refresh rate when using 3D audio positioning\r\n         */\r\n        audioPositioningRefreshRate: number;\r\n    }\r\n}\r\n\r\nObject.defineProperty(Scene.prototype, \"mainSoundTrack\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (!this._mainSoundTrack) {\r\n            this._mainSoundTrack = new SoundTrack(this, { mainTrack: true });\r\n        }\r\n\r\n        return this._mainSoundTrack;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nScene.prototype.getSoundByName = function (name: string): Nullable<Sound> {\r\n    let index: number;\r\n    for (index = 0; index < this.mainSoundTrack.soundCollection.length; index++) {\r\n        if (this.mainSoundTrack.soundCollection[index].name === name) {\r\n            return this.mainSoundTrack.soundCollection[index];\r\n        }\r\n    }\r\n\r\n    if (this.soundTracks) {\r\n        for (let sdIndex = 0; sdIndex < this.soundTracks.length; sdIndex++) {\r\n            for (index = 0; index < this.soundTracks[sdIndex].soundCollection.length; index++) {\r\n                if (this.soundTracks[sdIndex].soundCollection[index].name === name) {\r\n                    return this.soundTracks[sdIndex].soundCollection[index];\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    return null;\r\n};\r\n\r\nObject.defineProperty(Scene.prototype, \"audioEnabled\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioEnabled;\r\n    },\r\n    set: function (this: Scene, value: boolean) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value) {\r\n            compo.enableAudio();\r\n        } else {\r\n            compo.disableAudio();\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"headphone\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.headphone;\r\n    },\r\n    set: function (this: Scene, value: boolean) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (value) {\r\n            compo.switchAudioModeForHeadphones();\r\n        } else {\r\n            compo.switchAudioModeForNormalSpeakers();\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioListenerPositionProvider\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioListenerPositionProvider;\r\n    },\r\n    set: function (this: Scene, value: () => Vector3) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (typeof value !== \"function\") {\r\n            throw new Error(\"The value passed to [Scene.audioListenerPositionProvider] must be a function that returns a Vector3\");\r\n        } else {\r\n            compo.audioListenerPositionProvider = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioListenerRotationProvider\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioListenerRotationProvider;\r\n    },\r\n    set: function (this: Scene, value: () => Vector3) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        if (typeof value !== \"function\") {\r\n            throw new Error(\"The value passed to [Scene.audioListenerRotationProvider] must be a function that returns a Vector3\");\r\n        } else {\r\n            compo.audioListenerRotationProvider = value;\r\n        }\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\nObject.defineProperty(Scene.prototype, \"audioPositioningRefreshRate\", {\r\n    get: function (this: Scene) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        return compo.audioPositioningRefreshRate;\r\n    },\r\n    set: function (this: Scene, value: number) {\r\n        let compo = this._getComponent(SceneComponentConstants.NAME_AUDIO) as AudioSceneComponent;\r\n        if (!compo) {\r\n            compo = new AudioSceneComponent(this);\r\n            this._addComponent(compo);\r\n        }\r\n\r\n        compo.audioPositioningRefreshRate = value;\r\n    },\r\n    enumerable: true,\r\n    configurable: true,\r\n});\r\n\r\n/**\r\n * Defines the sound scene component responsible to manage any sounds\r\n * in a given scene.\r\n */\r\nexport class AudioSceneComponent implements ISceneSerializableComponent {\r\n    private static _CameraDirection = new Vector3(0, 0, -1);\r\n\r\n    /**\r\n     * The component name helpful to identify the component in the list of scene components.\r\n     */\r\n    public readonly name = SceneComponentConstants.NAME_AUDIO;\r\n\r\n    /**\r\n     * The scene the component belongs to.\r\n     */\r\n    public scene: Scene;\r\n\r\n    private _audioEnabled = true;\r\n    /**\r\n     * Gets whether audio is enabled or not.\r\n     * Please use related enable/disable method to switch state.\r\n     */\r\n    public get audioEnabled(): boolean {\r\n        return this._audioEnabled;\r\n    }\r\n\r\n    private _headphone = false;\r\n    /**\r\n     * Gets whether audio is outputting to headphone or not.\r\n     * Please use the according Switch methods to change output.\r\n     */\r\n    public get headphone(): boolean {\r\n        return this._headphone;\r\n    }\r\n\r\n    /**\r\n     * Gets or sets a refresh rate when using 3D audio positioning\r\n     */\r\n    public audioPositioningRefreshRate = 500;\r\n\r\n    /**\r\n     * Gets or Sets a custom listener position for all sounds in the scene\r\n     * By default, this is the position of the first active camera\r\n     */\r\n    public audioListenerPositionProvider: Nullable<() => Vector3> = null;\r\n\r\n    /**\r\n     * Gets or Sets a custom listener rotation for all sounds in the scene\r\n     * By default, this is the rotation of the first active camera\r\n     */\r\n    public audioListenerRotationProvider: Nullable<() => Vector3> = null;\r\n\r\n    /**\r\n     * Creates a new instance of the component for the given scene\r\n     * @param scene Defines the scene to register the component in\r\n     */\r\n    constructor(scene?: Nullable<Scene>) {\r\n        scene = scene || EngineStore.LastCreatedScene;\r\n        if (!scene) {\r\n            return;\r\n        }\r\n        this.scene = scene;\r\n\r\n        scene.soundTracks = new Array<SoundTrack>();\r\n        scene.sounds = new Array<Sound>();\r\n    }\r\n\r\n    /**\r\n     * Registers the component in a given scene\r\n     */\r\n    public register(): void {\r\n        this.scene._afterRenderStage.registerStep(SceneComponentConstants.STEP_AFTERRENDER_AUDIO, this, this._afterRender);\r\n    }\r\n\r\n    /**\r\n     * Rebuilds the elements related to this component in case of\r\n     * context lost for instance.\r\n     */\r\n    public rebuild(): void {\r\n        // Nothing to do here. (Not rendering related)\r\n    }\r\n\r\n    /**\r\n     * Serializes the component data to the specified json object\r\n     * @param serializationObject The object to serialize to\r\n     */\r\n    public serialize(serializationObject: any): void {\r\n        serializationObject.sounds = [];\r\n\r\n        if (this.scene.soundTracks) {\r\n            for (let index = 0; index < this.scene.soundTracks.length; index++) {\r\n                const soundtrack = this.scene.soundTracks[index];\r\n\r\n                for (let soundId = 0; soundId < soundtrack.soundCollection.length; soundId++) {\r\n                    serializationObject.sounds.push(soundtrack.soundCollection[soundId].serialize());\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Adds all the elements from the container to the scene\r\n     * @param container the container holding the elements\r\n     */\r\n    public addFromContainer(container: AbstractScene): void {\r\n        if (!container.sounds) {\r\n            return;\r\n        }\r\n        container.sounds.forEach((sound) => {\r\n            sound.play();\r\n            sound.autoplay = true;\r\n            this.scene.mainSoundTrack.addSound(sound);\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Removes all the elements in the container from the scene\r\n     * @param container contains the elements to remove\r\n     * @param dispose if the removed element should be disposed (default: false)\r\n     */\r\n    public removeFromContainer(container: AbstractScene, dispose = false): void {\r\n        if (!container.sounds) {\r\n            return;\r\n        }\r\n        container.sounds.forEach((sound) => {\r\n            sound.stop();\r\n            sound.autoplay = false;\r\n            this.scene.mainSoundTrack.removeSound(sound);\r\n            if (dispose) {\r\n                sound.dispose();\r\n            }\r\n        });\r\n    }\r\n\r\n    /**\r\n     * Disposes the component and the associated resources.\r\n     */\r\n    public dispose(): void {\r\n        const scene = this.scene;\r\n        if (scene._mainSoundTrack) {\r\n            scene.mainSoundTrack.dispose();\r\n        }\r\n\r\n        if (scene.soundTracks) {\r\n            for (let scIndex = 0; scIndex < scene.soundTracks.length; scIndex++) {\r\n                scene.soundTracks[scIndex].dispose();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Disables audio in the associated scene.\r\n     */\r\n    public disableAudio() {\r\n        const scene = this.scene;\r\n        this._audioEnabled = false;\r\n\r\n        if (Engine.audioEngine && Engine.audioEngine.audioContext) {\r\n            Engine.audioEngine.audioContext.suspend();\r\n        }\r\n\r\n        let i: number;\r\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n            scene.mainSoundTrack.soundCollection[i].pause();\r\n        }\r\n        if (scene.soundTracks) {\r\n            for (i = 0; i < scene.soundTracks.length; i++) {\r\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                    scene.soundTracks[i].soundCollection[j].pause();\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Enables audio in the associated scene.\r\n     */\r\n    public enableAudio() {\r\n        const scene = this.scene;\r\n        this._audioEnabled = true;\r\n\r\n        if (Engine.audioEngine && Engine.audioEngine.audioContext) {\r\n            Engine.audioEngine.audioContext.resume();\r\n        }\r\n\r\n        let i: number;\r\n        for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n            if (scene.mainSoundTrack.soundCollection[i].isPaused) {\r\n                scene.mainSoundTrack.soundCollection[i].play();\r\n            }\r\n        }\r\n        if (scene.soundTracks) {\r\n            for (i = 0; i < scene.soundTracks.length; i++) {\r\n                for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                    if (scene.soundTracks[i].soundCollection[j].isPaused) {\r\n                        scene.soundTracks[i].soundCollection[j].play();\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch audio to headphone output.\r\n     */\r\n    public switchAudioModeForHeadphones() {\r\n        const scene = this.scene;\r\n        this._headphone = true;\r\n\r\n        scene.mainSoundTrack.switchPanningModelToHRTF();\r\n        if (scene.soundTracks) {\r\n            for (let i = 0; i < scene.soundTracks.length; i++) {\r\n                scene.soundTracks[i].switchPanningModelToHRTF();\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Switch audio to normal speakers.\r\n     */\r\n    public switchAudioModeForNormalSpeakers() {\r\n        const scene = this.scene;\r\n        this._headphone = false;\r\n\r\n        scene.mainSoundTrack.switchPanningModelToEqualPower();\r\n\r\n        if (scene.soundTracks) {\r\n            for (let i = 0; i < scene.soundTracks.length; i++) {\r\n                scene.soundTracks[i].switchPanningModelToEqualPower();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _cachedCameraDirection = new Vector3();\r\n    private _cachedCameraPosition = new Vector3();\r\n    private _lastCheck = 0;\r\n    private _invertMatrixTemp = new Matrix();\r\n    private _cameraDirectionTemp = new Vector3();\r\n\r\n    private _afterRender() {\r\n        const now = PrecisionDate.Now;\r\n        if (this._lastCheck && now - this._lastCheck < this.audioPositioningRefreshRate) {\r\n            return;\r\n        }\r\n\r\n        this._lastCheck = now;\r\n\r\n        const scene = this.scene;\r\n        if (!this._audioEnabled || !scene._mainSoundTrack || !scene.soundTracks || (scene._mainSoundTrack.soundCollection.length === 0 && scene.soundTracks.length === 1)) {\r\n            return;\r\n        }\r\n\r\n        const audioEngine = Engine.audioEngine;\r\n\r\n        if (!audioEngine) {\r\n            return;\r\n        }\r\n\r\n        if (audioEngine.audioContext) {\r\n            let listeningCamera = scene.activeCamera;\r\n            if (scene.activeCameras && scene.activeCameras.length > 0) {\r\n                listeningCamera = scene.activeCameras[0];\r\n            }\r\n\r\n            // A custom listener position provider was set\r\n            // Use the users provided position instead of camera's\r\n            if (this.audioListenerPositionProvider) {\r\n                const position: Vector3 = this.audioListenerPositionProvider();\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setPosition(position.x || 0, position.y || 0, position.z || 0);\r\n                // Check if there is a listening camera\r\n            } else if (listeningCamera) {\r\n                // Set the listener position to the listening camera global position\r\n                if (!this._cachedCameraPosition.equals(listeningCamera.globalPosition)) {\r\n                    this._cachedCameraPosition.copyFrom(listeningCamera.globalPosition);\r\n                    audioEngine.audioContext.listener.setPosition(listeningCamera.globalPosition.x, listeningCamera.globalPosition.y, listeningCamera.globalPosition.z);\r\n                }\r\n            }\r\n            // Otherwise set the listener position to 0, 0 ,0\r\n            else {\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setPosition(0, 0, 0);\r\n            }\r\n\r\n            // A custom listener rotation provider was set\r\n            // Use the users provided rotation instead of camera's\r\n            if (this.audioListenerRotationProvider) {\r\n                const rotation: Vector3 = this.audioListenerRotationProvider();\r\n                audioEngine.audioContext.listener.setOrientation(rotation.x || 0, rotation.y || 0, rotation.z || 0, 0, 1, 0);\r\n                // Check if there is a listening camera\r\n            } else if (listeningCamera) {\r\n                // for VR cameras\r\n                if (listeningCamera.rigCameras && listeningCamera.rigCameras.length > 0) {\r\n                    listeningCamera = listeningCamera.rigCameras[0];\r\n                }\r\n\r\n                listeningCamera.getViewMatrix().invertToRef(this._invertMatrixTemp);\r\n                Vector3.TransformNormalToRef(AudioSceneComponent._CameraDirection, this._invertMatrixTemp, this._cameraDirectionTemp);\r\n                this._cameraDirectionTemp.normalize();\r\n                // To avoid some errors on GearVR\r\n                if (!isNaN(this._cameraDirectionTemp.x) && !isNaN(this._cameraDirectionTemp.y) && !isNaN(this._cameraDirectionTemp.z)) {\r\n                    if (!this._cachedCameraDirection.equals(this._cameraDirectionTemp)) {\r\n                        this._cachedCameraDirection.copyFrom(this._cameraDirectionTemp);\r\n                        audioEngine.audioContext.listener.setOrientation(this._cameraDirectionTemp.x, this._cameraDirectionTemp.y, this._cameraDirectionTemp.z, 0, 1, 0);\r\n                    }\r\n                }\r\n            }\r\n            // Otherwise set the listener rotation to 0, 0 ,0\r\n            else {\r\n                // Set the listener position\r\n                audioEngine.audioContext.listener.setOrientation(0, 0, 0, 0, 1, 0);\r\n            }\r\n\r\n            let i: number;\r\n            for (i = 0; i < scene.mainSoundTrack.soundCollection.length; i++) {\r\n                const sound = scene.mainSoundTrack.soundCollection[i];\r\n                if (sound.useCustomAttenuation) {\r\n                    sound.updateDistanceFromListener();\r\n                }\r\n            }\r\n            if (scene.soundTracks) {\r\n                for (i = 0; i < scene.soundTracks.length; i++) {\r\n                    for (let j = 0; j < scene.soundTracks[i].soundCollection.length; j++) {\r\n                        const sound = scene.soundTracks[i].soundCollection[j];\r\n                        if (sound.useCustomAttenuation) {\r\n                            sound.updateDistanceFromListener();\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nSound._SceneComponentInitialization = (scene: Scene) => {\r\n    let compo = scene._getComponent(SceneComponentConstants.NAME_AUDIO);\r\n    if (!compo) {\r\n        compo = new AudioSceneComponent(scene);\r\n        scene._addComponent(compo);\r\n    }\r\n};\r\n"]}