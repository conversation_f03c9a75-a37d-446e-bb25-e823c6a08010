import type { Animatable } from "./animatable";
import { Animation } from "./animation";
import type { Scene, IDisposable } from "../scene";
import { Observable } from "../Misc/observable";
import type { Nullable } from "../types";
import type { AbstractScene } from "../abstractScene";
/**
 * This class defines the direct association between an animation and a target
 */
export declare class TargetedAnimation {
    /**
     * Animation to perform
     */
    animation: Animation;
    /**
     * Target to animate
     */
    target: any;
    /**
     * Returns the string "TargetedAnimation"
     * @returns "TargetedAnimation"
     */
    getClassName(): string;
    /**
     * Serialize the object
     * @returns the JSON object representing the current entity
     */
    serialize(): any;
}
/**
 * Use this class to create coordinated animations on multiple targets
 */
export declare class AnimationGroup implements IDisposable {
    /** The name of the animation group */
    name: string;
    private _scene;
    private _targetedAnimations;
    private _animatables;
    private _from;
    private _to;
    private _isStarted;
    private _isPaused;
    private _speedRatio;
    private _loopAnimation;
    private _isAdditive;
    /** @internal */
    _parentContainer: Nullable<AbstractScene>;
    /**
     * Gets or sets the unique id of the node
     */
    uniqueId: number;
    /**
     * This observable will notify when one animation have ended
     */
    onAnimationEndObservable: Observable<TargetedAnimation>;
    /**
     * Observer raised when one animation loops
     */
    onAnimationLoopObservable: Observable<TargetedAnimation>;
    /**
     * Observer raised when all animations have looped
     */
    onAnimationGroupLoopObservable: Observable<AnimationGroup>;
    /**
     * This observable will notify when all animations have ended.
     */
    onAnimationGroupEndObservable: Observable<AnimationGroup>;
    /**
     * This observable will notify when all animations have paused.
     */
    onAnimationGroupPauseObservable: Observable<AnimationGroup>;
    /**
     * This observable will notify when all animations are playing.
     */
    onAnimationGroupPlayObservable: Observable<AnimationGroup>;
    /**
     * Gets or sets an object used to store user defined information for the node
     */
    metadata: any;
    /**
     * Gets the first frame
     */
    get from(): number;
    /**
     * Gets the last frame
     */
    get to(): number;
    /**
     * Define if the animations are started
     */
    get isStarted(): boolean;
    /**
     * Gets a value indicating that the current group is playing
     */
    get isPlaying(): boolean;
    /**
     * Gets or sets the speed ratio to use for all animations
     */
    get speedRatio(): number;
    /**
     * Gets or sets the speed ratio to use for all animations
     */
    set speedRatio(value: number);
    /**
     * Gets or sets if all animations should loop or not
     */
    get loopAnimation(): boolean;
    set loopAnimation(value: boolean);
    /**
     * Gets or sets if all animations should be evaluated additively
     */
    get isAdditive(): boolean;
    set isAdditive(value: boolean);
    /**
     * Gets the targeted animations for this animation group
     */
    get targetedAnimations(): Array<TargetedAnimation>;
    /**
     * returning the list of animatables controlled by this animation group.
     */
    get animatables(): Array<Animatable>;
    /**
     * Gets the list of target animations
     */
    get children(): TargetedAnimation[];
    /**
     * Instantiates a new Animation Group.
     * This helps managing several animations at once.
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/groupAnimations
     * @param name Defines the name of the group
     * @param scene Defines the scene the group belongs to
     */
    constructor(
    /** The name of the animation group */
    name: string, scene?: Nullable<Scene>);
    /**
     * Add an animation (with its target) in the group
     * @param animation defines the animation we want to add
     * @param target defines the target of the animation
     * @returns the TargetedAnimation object
     */
    addTargetedAnimation(animation: Animation, target: any): TargetedAnimation;
    /**
     * This function will normalize every animation in the group to make sure they all go from beginFrame to endFrame
     * It can add constant keys at begin or end
     * @param beginFrame defines the new begin frame for all animations or the smallest begin frame of all animations if null (defaults to null)
     * @param endFrame defines the new end frame for all animations or the largest end frame of all animations if null (defaults to null)
     * @returns the animation group
     */
    normalize(beginFrame?: Nullable<number>, endFrame?: Nullable<number>): AnimationGroup;
    private _animationLoopCount;
    private _animationLoopFlags;
    private _processLoop;
    /**
     * Start all animations on given targets
     * @param loop defines if animations must loop
     * @param speedRatio defines the ratio to apply to animation speed (1 by default)
     * @param from defines the from key (optional)
     * @param to defines the to key (optional)
     * @param isAdditive defines the additive state for the resulting animatables (optional)
     * @returns the current animation group
     */
    start(loop?: boolean, speedRatio?: number, from?: number, to?: number, isAdditive?: boolean): AnimationGroup;
    /**
     * Pause all animations
     * @returns the animation group
     */
    pause(): AnimationGroup;
    /**
     * Play all animations to initial state
     * This function will start() the animations if they were not started or will restart() them if they were paused
     * @param loop defines if animations must loop
     * @returns the animation group
     */
    play(loop?: boolean): AnimationGroup;
    /**
     * Reset all animations to initial state
     * @returns the animation group
     */
    reset(): AnimationGroup;
    /**
     * Restart animations from key 0
     * @returns the animation group
     */
    restart(): AnimationGroup;
    /**
     * Stop all animations
     * @returns the animation group
     */
    stop(): AnimationGroup;
    /**
     * Set animation weight for all animatables
     * @param weight defines the weight to use
     * @returns the animationGroup
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights
     */
    setWeightForAllAnimatables(weight: number): AnimationGroup;
    /**
     * Synchronize and normalize all animatables with a source animatable
     * @param root defines the root animatable to synchronize with (null to stop synchronizing)
     * @returns the animationGroup
     * @see https://doc.babylonjs.com/features/featuresDeepDive/animation/advanced_animations#animation-weights
     */
    syncAllAnimationsWith(root: Nullable<Animatable>): AnimationGroup;
    /**
     * Goes to a specific frame in this animation group
     * @param frame the frame number to go to
     * @returns the animationGroup
     */
    goToFrame(frame: number): AnimationGroup;
    /**
     * Dispose all associated resources
     */
    dispose(): void;
    private _checkAnimationGroupEnded;
    /**
     * Clone the current animation group and returns a copy
     * @param newName defines the name of the new group
     * @param targetConverter defines an optional function used to convert current animation targets to new ones
     * @param cloneAnimations defines if the animations should be cloned or referenced
     * @returns the new animation group
     */
    clone(newName: string, targetConverter?: (oldTarget: any) => any, cloneAnimations?: boolean): AnimationGroup;
    /**
     * Serializes the animationGroup to an object
     * @returns Serialized object
     */
    serialize(): any;
    /**
     * Returns a new AnimationGroup object parsed from the source provided.
     * @param parsedAnimationGroup defines the source
     * @param scene defines the scene that will receive the animationGroup
     * @returns a new AnimationGroup
     */
    static Parse(parsedAnimationGroup: any, scene: Scene): AnimationGroup;
    /**
     * Convert the keyframes for all animations belonging to the group to be relative to a given reference frame.
     * @param sourceAnimationGroup defines the AnimationGroup containing animations to convert
     * @param referenceFrame defines the frame that keyframes in the range will be relative to
     * @param range defines the name of the AnimationRange belonging to the animations in the group to convert
     * @param cloneOriginal defines whether or not to clone the group and convert the clone or convert the original group (default is false)
     * @param clonedName defines the name of the resulting cloned AnimationGroup if cloneOriginal is true
     * @returns a new AnimationGroup if cloneOriginal is true or the original AnimationGroup if cloneOriginal is false
     */
    static MakeAnimationAdditive(sourceAnimationGroup: AnimationGroup, referenceFrame?: number, range?: string, cloneOriginal?: boolean, clonedName?: string): AnimationGroup;
    /**
     * Returns the string "AnimationGroup"
     * @returns "AnimationGroup"
     */
    getClassName(): string;
    /**
     * Creates a detailed string about the object
     * @param fullDetails defines if the output string will support multiple levels of logging within scene loading
     * @returns a string representing the object
     */
    toString(fullDetails?: boolean): string;
}
