/* Musicmodules/music.css - Modern Glassmorphism Redesign */

/* ------------------------- */
/* --- 变量和全局样式 --- */
/* ------------------------- */

:root {
    /* 从主样式继承颜色变量 (已移至 body 选择器) */
    
    /* 玻璃效果变量 (深色主题) */
    --glass-bg: rgba(20, 20, 20, 0.6);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.3);
    --hover-bg: rgba(255, 255, 255, 0.08);

    /* 字体 */
    --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", Arial, sans-serif;
}

/* 浅色主题下的变量覆盖 */
body.light-theme {
    --glass-bg: rgba(255, 255, 255, 0.65);
    --glass-border: rgba(0, 0, 0, 0.1);
    --glass-shadow: rgba(0, 0, 0, 0.1);
    --hover-bg: rgba(0, 0, 0, 0.05);
}

body {
    /* 从主样式继承颜色变量 */
    --music-bg: var(--primary-bg);
    --music-text: var(--primary-text);
    --music-text-secondary: var(--secondary-text);
    --music-highlight: var(--highlight-text);
    --music-border: var(--border-color);

    margin: 0;
    font-family: var(--font-family);
    color: var(--music-text);
    background-color: transparent; /* body背景透明，让主程序背景透出 */
    overflow: hidden; /* 防止出现不必要的滚动条 */
}

/* ------------------------- */
/* --- 布局和背景 --- */
/* ------------------------- */

#player-background {
    position: fixed;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background-size: cover;
    background-position: center;
    filter: blur(25px) brightness(0.6);
    transform: scale(1.1);
    transition: background-image 0.8s ease-in-out;
    z-index: -1; /* 置于最底层 */
}

.music-player-container {
    display: flex;
    height: 100vh;
    padding: 20px;
    box-sizing: border-box;
    gap: 20px;
}

.main-content {
    flex: 1.618; /* Golden ratio grow */
    display: flex;
    flex-direction: column;
    gap: 20px;
    overflow: hidden;
    min-width: 400px; /* Prevent it from becoming too small */
}

.right-sidebar {
    flex: 1; /* Golden ratio grow */
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 320px; /* Keep sidebar usable */
}

.settings-container {
    flex-shrink: 0;
}

.lyrics-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start; /* 歌词从顶部开始显示 */
    overflow: hidden; /* Crucial for smooth scrolling */
    position: relative;
}

#lyrics-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: center;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* Smoother transition */
    width: 100%;
    /* Add padding to top and bottom to allow first and last lines to reach the golden ratio spot */
    padding: 0;
}

#lyrics-list li {
    font-size: 1.1em;
    line-height: 1.6;
    color: var(--music-text-secondary);
    transition: color 0.4s ease, font-size 0.4s ease, font-weight 0.4s ease, opacity 0.4s ease, transform 0.3s ease;
    font-weight: 500;
    padding: 5px 15px;
    opacity: 0.5; /* Default opacity for non-active lines */
    transform: scale(0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.lyric-original, .lyric-translation {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}

.lyric-translation {
    font-size: 0.85em;
    opacity: 0.8;
    margin-top: 4px;
}

#lyrics-list li.active {
    color: var(--music-text) !important;
    font-weight: 700;
    font-size: 1.3em;
    opacity: 1;
    transform: scale(1);
}

#lyrics-list li.active .lyric-original {
    color: var(--music-highlight);
}

#lyrics-list li.active .lyric-translation {
    color: var(--music-highlight);
    opacity: 1;
}

#lyrics-list .no-lyrics {
    font-size: 1.2em;
    color: var(--music-text-secondary);
}

/* 核心玻璃面板样式 */
.glass-panel {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px 0 var(--glass-shadow);
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: background 0.3s ease, border 0.3s ease;
}

/* ------------------------- */
/* --- 播放器控制区 --- */
/* ------------------------- */

.music-player {
    flex-shrink: 0; /* 防止被压缩 */
}

.track-info {
    display: flex;
    align-items: flex-start; /* 改为顶部对齐，以解决按钮垂直位置问题 */
    gap: 20px;
    margin-bottom: 20px;
}

#share-btn {
    margin-left: auto; /* 将按钮推到右侧 */
}

.album-art-wrapper {
    width: 90px;
    height: 90px;
    flex-shrink: 0;
    border-radius: 16px;
    box-shadow: 0 4px 15px var(--glass-shadow);
    transition: transform 0.3s ease;
}
.album-art-wrapper:hover {
    transform: scale(1.05);
}

.album-art {
    width: 100%;
    height: 100%;
    border-radius: 16px;
    background-size: cover;
    background-position: center;
    background-image: url('../assets/musicdark.jpeg'); /* 默认封面 */
    transition: background-image 0.5s ease-in-out;
}
body.light-theme .album-art {
    background-image: url('../assets/musiclight.jpeg');
}

.track-details {
    overflow: hidden; /* 防止文字过长溢出 */
}

.track-title {
    font-size: 1.5em;
    font-weight: 600;
    color: var(--music-text);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-artist {
    font-size: 1em;
    color: var(--music-text-secondary);
    font-weight: 400;
    margin-top: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.track-bitrate {
    font-size: 0.9em;
    color: var(--music-text-secondary);
    margin-top: 4px;
}


/* 进度条 */
.progress-section {
    margin-bottom: 15px;
}

/* 可视化工具样式 */
#visualizer {
    width: 100%;
    height: 80px;
    display: block;
    margin-bottom: 10px;
}

.progress-container {
    width: 100%;
    padding: 5px 0; /* 增加点击区域 */
    cursor: pointer;
}

.progress-bar {
    width: 100%;
    height: 6px;
    background-color: var(--hover-bg);
    border-radius: 3px;
    overflow: hidden;
}

.progress {
    width: 0;
    height: 100%;
    background-color: var(--music-highlight);
    border-radius: 3px;
    transition: width 0.1s linear;
}

.time-display {
    display: flex;
    justify-content: space-between;
    font-size: 0.75em;
    color: var(--music-text-secondary);
    margin-top: 6px;
}

/* 控制按钮 */
.controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.control-btn {
    background: transparent;
    border: none;
    color: var(--music-text-secondary);
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: color 0.3s ease, background-color 0.3s ease, transform 0.2s ease;
}

.control-btn:hover {
    color: var(--music-text);
    background-color: var(--hover-bg);
}
.control-btn:active {
    transform: scale(0.9);
}

.control-btn.play-btn {
    background-color: var(--music-highlight);
    color: white;
    width: 56px;
    height: 56px;
    border-radius: 50%;
}
.control-btn.play-btn:hover {
    filter: brightness(1.1);
}
.control-btn.play-btn svg {
    width: 32px;
    height: 32px;
}

.play-icon { display: block; }
.pause-icon { display: none; }
.control-btn.is-playing .play-icon { display: none; }
.control-btn.is-playing .pause-icon { display: block; }

/* 音量控制 */
.volume-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.volume-icon { display: block; }
.mute-icon { display: none; }
#volume-btn.is-muted .volume-icon { display: none; }
#volume-btn.is-muted .mute-icon { display: block; }

#volume-slider {
    -webkit-appearance: none;
    appearance: none;
    width: 80px;
    height: 5px;
    background-color: var(--hover-bg);
    background-image: linear-gradient(to right, var(--music-highlight), var(--music-highlight));
    background-repeat: no-repeat;
    background-size: 0% 100%; /* Default to 0, will be updated by JS */
    border-radius: 3px;
    outline: none;
    transition: opacity 0.2s;
    cursor: pointer;
}

#volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 14px;
    height: 14px;
    background: var(--music-text);
    border-radius: 50%;
    transition: background 0.3s ease;
}
#volume-slider::-webkit-slider-thumb:hover {
    background: var(--music-highlight);
}
#volume-slider::-moz-range-thumb {
    width: 14px;
    height: 14px;
    background: var(--music-text);
    border-radius: 50%;
    transition: background 0.3s ease;
}
#volume-slider::-moz-range-thumb:hover {
    background: var(--music-highlight);
}


/* 播放模式按钮 */
#mode-btn {
    -webkit-mask-size: cover;
    mask-size: cover;
    background-color: var(--music-text-secondary);
    width: 24px;
    height: 24px;
}
#mode-btn:hover {
    background-color: var(--music-text);
}
#mode-btn.active {
    background-color: var(--music-highlight);
}
#mode-btn.repeat {
    -webkit-mask-image: url('../assets/repeat.svg');
    mask-image: url('../assets/repeat.svg');
}
#mode-btn.repeat-one {
    -webkit-mask-image: url('../assets/repeat-one.svg');
    mask-image: url('../assets/repeat-one.svg');
}
#mode-btn.shuffle {
    -webkit-mask-image: url('../assets/shuffle.svg');
    mask-image: url('../assets/shuffle.svg');
}


/* ------------------------- */
/* --- 播放列表 --- */
/* ------------------------- */

.playlist-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出玻璃面板 */
    padding-top: 15px; /* Add some padding to the top */
}

.playlist-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-shrink: 0;
}
.playlist-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
}
.playlist-actions {
    display: flex;
    gap: 10px;
}

#search-input, #add-folder-btn {
    border: none;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 0.9em;
}

#search-input {
    background-color: var(--hover-bg);
    color: var(--music-text);
    width: 150px;
    transition: background-color 0.3s ease;
}
#search-input:focus {
    outline: none;
    background-color: rgba(255, 255, 255, 0.15);
}
body.light-theme #search-input:focus {
    background-color: rgba(0, 0, 0, 0.1);
}

#add-folder-btn {
    background-color: var(--music-highlight);
    color: white;
    cursor: pointer;
    transition: filter 0.2s ease;
}
#add-folder-btn:hover {
    filter: brightness(1.15);
}

/* --- New Settings Section Styles --- */
.settings-section {
    display: flex;
    flex-direction: column; /* Stack items vertically */
    padding: 0;
    gap: 15px; /* Adjust gap */
    border-bottom: 1px solid var(--glass-border);
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.device-selection, .wasapi-toggle, .upsampling-selection {
    width: 100%; /* Make items take full width */
}

.wasapi-toggle {
    justify-content: space-between; /* Push label and switch to opposite ends */
}

.device-selection, .upsampling-selection {
   display: flex;
   flex-direction: column; /* Stack label and select vertically */
   align-items: flex-start;
   gap: 8px;
   font-size: 0.9em;
   color: var(--music-text-secondary);
}

#device-select, #upsampling-select {
    background-color: var(--hover-bg);
    color: var(--music-text);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    padding: 5px 8px;
    outline: none;
    width: 100%; /* Make it fill the container */
    box-sizing: border-box; /* Include padding and border in the element's total width and height */
}

#upsampling-select {
    flex-grow: 1;
}
 
 /* --- Toggle Switch Styles --- */
.switch {
 position: relative;
 display: inline-block;
 width: 44px;
 height: 24px;
}

.switch input {
 opacity: 0;
 width: 0;
 height: 0;
}

.slider {
 position: absolute;
 cursor: pointer;
 top: 0;
 left: 0;
 right: 0;
 bottom: 0;
 background-color: var(--hover-bg);
 transition: .4s;
}

.slider:before {
 position: absolute;
 content: "";
 height: 18px;
 width: 18px;
 left: 3px;
 bottom: 3px;
 background-color: white;
 transition: .4s;
}

input:checked + .slider {
 background-color: var(--music-highlight);
}

input:focus + .slider {
 box-shadow: 0 0 1px var(--music-highlight);
}

input:checked + .slider:before {
 transform: translateX(20px);
}

.slider.round {
 border-radius: 24px;
}

.slider.round:before {
  border-radius: 50%;
}

/* --- New EQ Section Styles --- */
.eq-section {
   padding: 0;
}

.eq-header {
   display: flex;
   justify-content: space-between;
   align-items: center;
   margin-bottom: 15px;
}

.eq-header h4 {
    margin: 0;
    font-size: 1em;
    font-weight: 500;
    color: var(--music-text-secondary);
}

.eq-controls {
   display: flex;
   align-items: center;
   gap: 15px;
}

.eq-preset-select {
    background-color: var(--hover-bg);
    color: var(--music-text);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 0.8em;
    outline: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.eq-preset-select:hover {
    background-color: rgba(255, 255, 255, 0.15);
}

body.light-theme .eq-preset-select:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* 为下拉菜单选项在深色模式下设置深色背景 */
#device-select option,
#upsampling-select option,
.eq-preset-select option {
   background-color: #2c2c2c;
   color: var(--music-text);
}

/* 浅色主题下恢复默认 */
body.light-theme #device-select option,
body.light-theme #upsampling-select option,
body.light-theme .eq-preset-select option {
    background-color: white;
    color: initial;
}

.eq-bands {
   display: flex;
   justify-content: space-around;
   align-items: center;
   gap: 5px; /* Reduce gap for more bands */
   max-height: 0;
   overflow: hidden;
   transition: max-height 0.4s ease-out, padding 0.4s ease-out;
   padding-top: 0;
}

.eq-section.expanded .eq-bands {
   max-height: 200px; /* A height large enough to show the sliders */
   padding-top: 15px; /* Add some space when expanded */
}

.eq-band {
   display: flex;
   flex-direction: column;
   align-items: center;
   gap: 8px;
   /* Add a container for rotation */
   width: 30px;
   height: 120px;
   justify-content: center;
}

.eq-band label {
   font-size: 0.75em;
   color: var(--music-text-secondary);
}

.eq-band input[type="range"] {
   -webkit-appearance: none;
   appearance: none;
   transform: rotate(-90deg);
   width: 100px; /* This is the new height */
   height: 8px; /* This is the new width */
   background: var(--hover-bg);
   border-radius: 4px;
   outline: none;
   transition: opacity 0.2s;
   cursor: pointer;
}

.eq-band input[type="range"]::-webkit-slider-thumb {
   -webkit-appearance: none;
   appearance: none;
   width: 16px;
   height: 16px;
   background: var(--music-text);
   border-radius: 50%;
   transition: background 0.3s ease;
}
.eq-band input[type="range"]::-webkit-slider-thumb:hover {
   background: var(--music-highlight);
}

.eq-band input[type="range"]::-moz-range-thumb {
   width: 16px;
   height: 16px;
   background: var(--music-text);
   border-radius: 50%;
   transition: background 0.3s ease;
}
.eq-band input[type="range"]::-moz-range-thumb:hover {
   background: var(--music-highlight);
}

.playlist {
    list-style: none;
    padding: 0 10px 0 0; /* Add some padding to the right for the scrollbar */
    margin: 0;
    overflow-y: auto; /* 列表内容可滚动 */
    flex-grow: 1;
}
/* 自定义滚动条样式 */
.playlist::-webkit-scrollbar {
    width: 6px;
}
.playlist::-webkit-scrollbar-track {
    background: transparent;
}
.playlist::-webkit-scrollbar-thumb {
    background: var(--hover-bg);
    border-radius: 3px;
}
.playlist::-webkit-scrollbar-thumb:hover {
    background: var(--glass-border);
}

.playlist li {
    padding: 12px 15px;
    cursor: pointer;
    border-radius: 8px;
    margin-bottom: 4px; /* 代替边框线 */
    transition: background-color 0.2s ease;
    font-size: 0.95em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.playlist li:hover {
    background-color: var(--hover-bg);
}
.playlist li.active {
    background-color: var(--music-highlight);
    color: white;
    font-weight: 500;
}

/* ------------------------- */
/* --- 加载指示器 --- */
/* ------------------------- */

.loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    color: var(--music-text);
}

.spinner {
    border: 4px solid var(--hover-bg);
    border-top: 4px solid var(--music-highlight);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.scan-progress-container {
    width: 200px;
    height: 8px;
    background-color: var(--hover-bg);
    border-radius: 4px;
    overflow: hidden;
}

.scan-progress-bar {
    width: 0%;
    height: 100%;
    background-color: var(--music-highlight);
    transition: width 0.1s linear;
}

.scan-progress-label {
    font-size: 0.9em;
    color: var(--music-text-secondary);
}

/* ------------------------- */
/* --- 自定义标题栏 --- */
/* ------------------------- */

#custom-title-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
    background-color: transparent; /* 初始透明，可以根据主题调整 */
    z-index: 1000;
    -webkit-app-region: drag; /* 关键：允许拖动窗口 */
    user-select: none;
}

#custom-title-bar .title {
    font-size: 14px;
    font-weight: 600;
    color: var(--music-text);
    -webkit-app-region: no-drag; /* 标题文字区域不可拖动 */
}

#custom-title-bar .window-controls {
    display: flex;
    gap: 10px;
    -webkit-app-region: no-drag; /* 按钮区域不可拖动 */
}

.window-control-btn {
    width: 30px;
    height: 30px;
    border: none;
    background-color: transparent;
    color: var(--music-text-secondary);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease, color 0.2s ease;
}

.window-control-btn:hover {
    background-color: var(--hover-bg);
    color: var(--music-text);
}

#close-music-btn:hover {
    background-color: #e81123;
    color: white;
}

/* 调整主容器，为标题栏留出空间 */
.music-player-container {
    padding-top: 60px; /* 20px 原有 padding + 40px 标题栏高度 */
}
