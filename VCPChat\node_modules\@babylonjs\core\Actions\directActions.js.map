{"version": 3, "file": "directActions.js", "sourceRoot": "", "sources": ["../../../../lts/core/generated/Actions/directActions.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,OAAO,EAAE,MAAM,sBAAsB,CAAC;AAC/C,OAAO,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AAElC,OAAO,EAAE,SAAS,EAAE,MAAM,sBAAsB,CAAC;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,mBAAmB,CAAC;AAIlD;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,MAAM;IAU3C;;;;;;OAMG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,YAAoB,EAAE,SAAqB;QACrF,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IAClD,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACnF,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,qBAAqB;YAC3B,UAAU,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;SAC5G,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,cAAe,SAAQ,MAAM;IAQtC;;;;;;OAMG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,KAAa,EAAE,SAAqB;QAC9E,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;SAC9F,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,cAAe,SAAQ,MAAM;IAetC;;;;;;;OAOG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,YAAoB,EAAE,KAAU,EAAE,SAAqB;QACjG,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IAClD,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC1D,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;QAEnD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5C;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,gBAAgB;YACtB,UAAU,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACvC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;gBAClD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;aACvE;SACJ,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;;GAIG;AACH,MAAM,OAAO,oBAAqB,SAAQ,MAAM;IAe5C;;;;;;;OAOG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,YAAoB,EAAE,KAAU,EAAE,SAAqB;QACjG,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC;IAClD,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAC3F,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEtD,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,QAAQ,EAAE;YAC3D,MAAM,CAAC,IAAI,CAAC,mEAAmE,CAAC,CAAC;SACpF;IACL,CAAC;IAED;;OAEG;IACI,OAAO;QACV,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;QAEpD,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC5C;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,sBAAsB;YAC5B,UAAU,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACvC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE;gBAClD,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;aACvE;SACJ,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,MAAM;IAkB3C;;;;;;;;OAQG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,IAAY,EAAE,EAAU,EAAE,IAAc,EAAE,SAAqB;QACzG,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,QAAQ,KAAU,CAAC;IAE1B;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC7C,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;IACtE,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,qBAAqB;YAC3B,UAAU,EAAE;gBACR,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC;gBACvC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAC1C,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBACtC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,EAAE;aAC9E;SACJ,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,mBAAoB,SAAQ,MAAM;IAG3C;;;;;OAKG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,SAAqB;QAC/D,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,QAAQ,KAAU,CAAC;IAE1B;;OAEG;IACI,OAAO;QACV,MAAM,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,CAAC;QAC7C,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,qBAAqB;YAC3B,UAAU,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACxD,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,eAAgB,SAAQ,MAAM;IACvC;;;;OAIG;IACH,YAAY,iBAAsB,SAAS,CAAC,qBAAqB,EAAE,SAAqB;QACpF,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACI,OAAO,KAAU,CAAC;IAEzB;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,EAAE;SACjB,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,aAAc,SAAQ,MAAM;IAWrC;;;;;;OAMG;IACH,YAAY,cAAmB,EAAE,QAAkB,EAAE,SAAqB,EAAE,wBAAwB,GAAG,IAAI;QACvG,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,wBAAwB,GAAG,wBAAwB,CAAC;IAC7D,CAAC;IAED,gBAAgB;IACT,QAAQ;QACX,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE;YACvD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,CAAC;SACnC;IACL,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAChC,IAAI,CAAC,IAAI,CAAC,wBAAwB,IAAI,MAAM,CAAC,iCAAiC,EAAE,EAAE;gBAC9E,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;aACvB;SACJ;IACL,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,MAAM,mBAAmB,GAAG,KAAK,CAAC,UAAU,CACxC;YACI,IAAI,EAAE,eAAe;YACrB,UAAU,EAAE,EAAE;YACd,OAAO,EAAE,EAAE;SACd,EACD,MAAM,CACT,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC3C,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;SACtE;QAED,OAAO,mBAAmB,CAAC;IAC/B,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,iBAAkB,SAAQ,MAAM;IAMzC;;;;;OAKG;IACH,YAAY,cAAmB,EAAE,IAAgC,EAAE,SAAqB;QACpF,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACrB,CAAC;IAED;;;OAGG;IACI,OAAO,CAAC,GAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;CACJ;AAED;;;GAGG;AACH,MAAM,OAAO,eAAgB,SAAQ,MAAM;IAIvC;;;;;;OAMG;IACH,YAAY,cAAmB,EAAE,MAAW,EAAE,MAAW,EAAE,SAAqB;QAC5E,KAAK,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACT,QAAQ,KAAU,CAAC;IAE1B;;OAEG;IACI,OAAO;QACV,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,EAAE;YACtC,OAAO;SACV;QAED,MAAM,uBAAuB,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC,KAAK,EAAE,CAAC;QACtE,uBAAuB,CAAC,MAAM,EAAE,CAAC;QAEjC,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;QAErG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,SAAS,CAAC,MAAW;QACxB,OAAO,KAAK,CAAC,UAAU,CACnB;YACI,IAAI,EAAE,iBAAiB;YACvB,UAAU,EAAE,CAAC,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SACjG,EACD,MAAM,CACT,CAAC;IACN,CAAC;CACJ;AAED,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;AAC1D,aAAa,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC;AAC9D,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;AAC1D,aAAa,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;AAClE,aAAa,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;AAClE,aAAa,CAAC,8BAA8B,EAAE,oBAAoB,CAAC,CAAC;AACpE,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;AACxD,aAAa,CAAC,wBAAwB,EAAE,cAAc,CAAC,CAAC;AACxD,aAAa,CAAC,yBAAyB,EAAE,eAAe,CAAC,CAAC;AAC1D,aAAa,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,CAAC;AAClE,aAAa,CAAC,uBAAuB,EAAE,aAAa,CAAC,CAAC", "sourcesContent": ["import { Logger } from \"../Misc/logger\";\r\nimport { Vector3 } from \"../Maths/math.vector\";\r\nimport { Action } from \"./action\";\r\nimport type { Condition } from \"./condition\";\r\nimport { Constants } from \"../Engines/constants\";\r\nimport { RegisterClass } from \"../Misc/typeStore\";\r\n\r\ndeclare type ActionEvent = import(\"./actionEvent\").ActionEvent;\r\n\r\n/**\r\n * This defines an action responsible to toggle a boolean once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class SwitchBooleanAction extends Action {\r\n    /**\r\n     * The path to the boolean property in the target object\r\n     */\r\n    public propertyPath: string;\r\n\r\n    private _target: any;\r\n    private _effectiveTarget: any;\r\n    private _property: string;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the object containing the boolean\r\n     * @param propertyPath defines the path to the boolean property in the target object\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, propertyPath: string, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.propertyPath = propertyPath;\r\n        this._target = this._effectiveTarget = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {\r\n        this._effectiveTarget = this._getEffectiveTarget(this._effectiveTarget, this.propertyPath);\r\n        this._property = this._getProperty(this.propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Execute the action toggle the boolean value.\r\n     */\r\n    public execute(): void {\r\n        this._effectiveTarget[this._property] = !this._effectiveTarget[this._property];\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"SwitchBooleanAction\",\r\n                properties: [Action._GetTargetProperty(this._target), { name: \"propertyPath\", value: this.propertyPath }],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to set a the state field of the target\r\n *  to a desired value once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class SetStateAction extends Action {\r\n    /**\r\n     * The value to store in the state field.\r\n     */\r\n    public value: string;\r\n\r\n    private _target: any;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the object containing the state property\r\n     * @param value defines the value to store in the state field\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, value: string, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.value = value;\r\n        this._target = target;\r\n    }\r\n\r\n    /**\r\n     * Execute the action and store the value on the target state property.\r\n     */\r\n    public execute(): void {\r\n        this._target.state = this.value;\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"SetStateAction\",\r\n                properties: [Action._GetTargetProperty(this._target), { name: \"value\", value: this.value }],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to set a property of the target\r\n *  to a desired value once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class SetValueAction extends Action {\r\n    /**\r\n     * The path of the property to set in the target.\r\n     */\r\n    public propertyPath: string;\r\n\r\n    /**\r\n     * The value to set in the property\r\n     */\r\n    public value: any;\r\n\r\n    private _target: any;\r\n    private _effectiveTarget: any;\r\n    private _property: string;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the object containing the property\r\n     * @param propertyPath defines the path of the property to set in the target\r\n     * @param value defines the value to set in the property\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, propertyPath: string, value: any, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.propertyPath = propertyPath;\r\n        this.value = value;\r\n        this._target = this._effectiveTarget = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {\r\n        this._effectiveTarget = this._getEffectiveTarget(this._effectiveTarget, this.propertyPath);\r\n        this._property = this._getProperty(this.propertyPath);\r\n    }\r\n\r\n    /**\r\n     * Execute the action and set the targeted property to the desired value.\r\n     */\r\n    public execute(): void {\r\n        this._effectiveTarget[this._property] = this.value;\r\n\r\n        if (this._target.markAsDirty) {\r\n            this._target.markAsDirty(this._property);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"SetValueAction\",\r\n                properties: [\r\n                    Action._GetTargetProperty(this._target),\r\n                    { name: \"propertyPath\", value: this.propertyPath },\r\n                    { name: \"value\", value: Action._SerializeValueAsString(this.value) },\r\n                ],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to increment the target value\r\n *  to a desired value once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class IncrementValueAction extends Action {\r\n    /**\r\n     * The path of the property to increment in the target.\r\n     */\r\n    public propertyPath: string;\r\n\r\n    /**\r\n     * The value we should increment the property by.\r\n     */\r\n    public value: any;\r\n\r\n    private _target: any;\r\n    private _effectiveTarget: any;\r\n    private _property: string;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the object containing the property\r\n     * @param propertyPath defines the path of the property to increment in the target\r\n     * @param value defines the value value we should increment the property by\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, propertyPath: string, value: any, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.propertyPath = propertyPath;\r\n        this.value = value;\r\n        this._target = this._effectiveTarget = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {\r\n        this._effectiveTarget = this._getEffectiveTarget(this._effectiveTarget, this.propertyPath);\r\n        this._property = this._getProperty(this.propertyPath);\r\n\r\n        if (typeof this._effectiveTarget[this._property] !== \"number\") {\r\n            Logger.Warn(\"Warning: IncrementValueAction can only be used with number values\");\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Execute the action and increment the target of the value amount.\r\n     */\r\n    public execute(): void {\r\n        this._effectiveTarget[this._property] += this.value;\r\n\r\n        if (this._target.markAsDirty) {\r\n            this._target.markAsDirty(this._property);\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"IncrementValueAction\",\r\n                properties: [\r\n                    Action._GetTargetProperty(this._target),\r\n                    { name: \"propertyPath\", value: this.propertyPath },\r\n                    { name: \"value\", value: Action._SerializeValueAsString(this.value) },\r\n                ],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to start an animation once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class PlayAnimationAction extends Action {\r\n    /**\r\n     * Where the animation should start (animation frame)\r\n     */\r\n    public from: number;\r\n\r\n    /**\r\n     * Where the animation should stop (animation frame)\r\n     */\r\n    public to: number;\r\n\r\n    /**\r\n     * Define if the animation should loop or stop after the first play.\r\n     */\r\n    public loop?: boolean;\r\n\r\n    private _target: any;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the target animation or animation name\r\n     * @param from defines from where the animation should start (animation frame)\r\n     * @param to defines where the animation should stop (animation frame)\r\n     * @param loop defines if the animation should loop or stop after the first play\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, from: number, to: number, loop?: boolean, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.from = from;\r\n        this.to = to;\r\n        this.loop = loop;\r\n        this._target = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Execute the action and play the animation.\r\n     */\r\n    public execute(): void {\r\n        const scene = this._actionManager.getScene();\r\n        scene.beginAnimation(this._target, this.from, this.to, this.loop);\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"PlayAnimationAction\",\r\n                properties: [\r\n                    Action._GetTargetProperty(this._target),\r\n                    { name: \"from\", value: String(this.from) },\r\n                    { name: \"to\", value: String(this.to) },\r\n                    { name: \"loop\", value: Action._SerializeValueAsString(this.loop) || false },\r\n                ],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to stop an animation once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class StopAnimationAction extends Action {\r\n    private _target: any;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the target animation or animation name\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this._target = target;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Execute the action and stop the animation.\r\n     */\r\n    public execute(): void {\r\n        const scene = this._actionManager.getScene();\r\n        scene.stopAnimation(this._target);\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"StopAnimationAction\",\r\n                properties: [Action._GetTargetProperty(this._target)],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible that does nothing once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class DoNothingAction extends Action {\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any = Constants.ACTION_NothingTrigger, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n    }\r\n\r\n    /**\r\n     * Execute the action and do nothing.\r\n     */\r\n    public execute(): void {}\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"DoNothingAction\",\r\n                properties: [],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to trigger several actions once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class CombineAction extends Action {\r\n    /**\r\n     * The list of aggregated animations to run.\r\n     */\r\n    public children: Action[];\r\n\r\n    /**\r\n     * defines if the children actions conditions should be check before execution\r\n     */\r\n    public enableChildrenConditions: boolean;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param children defines the list of aggregated animations to run\r\n     * @param condition defines the trigger related conditions\r\n     * @param enableChildrenConditions defines if the children actions conditions should be check before execution\r\n     */\r\n    constructor(triggerOptions: any, children: Action[], condition?: Condition, enableChildrenConditions = true) {\r\n        super(triggerOptions, condition);\r\n        this.children = children;\r\n        this.enableChildrenConditions = enableChildrenConditions;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {\r\n        for (let index = 0; index < this.children.length; index++) {\r\n            this.children[index]._actionManager = this._actionManager;\r\n            this.children[index]._prepare();\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Execute the action and executes all the aggregated actions.\r\n     * @param evt\r\n     */\r\n    public execute(evt: ActionEvent): void {\r\n        for (const action of this.children) {\r\n            if (!this.enableChildrenConditions || action._evaluateConditionForCurrentFrame()) {\r\n                action.execute(evt);\r\n            }\r\n        }\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        const serializationObject = super._serialize(\r\n            {\r\n                name: \"CombineAction\",\r\n                properties: [],\r\n                combine: [],\r\n            },\r\n            parent\r\n        );\r\n\r\n        for (let i = 0; i < this.children.length; i++) {\r\n            serializationObject.combine.push(this.children[i].serialize(null));\r\n        }\r\n\r\n        return serializationObject;\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to run code (external event) once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class ExecuteCodeAction extends Action {\r\n    /**\r\n     * The callback function to run.\r\n     */\r\n    public func: (evt: ActionEvent) => void;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param func defines the callback function to run\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, func: (evt: ActionEvent) => void, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this.func = func;\r\n    }\r\n\r\n    /**\r\n     * Execute the action and run the attached code.\r\n     * @param evt\r\n     */\r\n    public execute(evt: ActionEvent): void {\r\n        this.func(evt);\r\n    }\r\n}\r\n\r\n/**\r\n * This defines an action responsible to set the parent property of the target once triggered.\r\n * @see https://doc.babylonjs.com/features/featuresDeepDive/events/actions\r\n */\r\nexport class SetParentAction extends Action {\r\n    private _parent: any;\r\n    private _target: any;\r\n\r\n    /**\r\n     * Instantiate the action\r\n     * @param triggerOptions defines the trigger options\r\n     * @param target defines the target containing the parent property\r\n     * @param parent defines from where the animation should start (animation frame)\r\n     * @param condition defines the trigger related conditions\r\n     */\r\n    constructor(triggerOptions: any, target: any, parent: any, condition?: Condition) {\r\n        super(triggerOptions, condition);\r\n        this._target = target;\r\n        this._parent = parent;\r\n    }\r\n\r\n    /** @internal */\r\n    public _prepare(): void {}\r\n\r\n    /**\r\n     * Execute the action and set the parent property.\r\n     */\r\n    public execute(): void {\r\n        if (this._target.parent === this._parent) {\r\n            return;\r\n        }\r\n\r\n        const invertParentWorldMatrix = this._parent.getWorldMatrix().clone();\r\n        invertParentWorldMatrix.invert();\r\n\r\n        this._target.position = Vector3.TransformCoordinates(this._target.position, invertParentWorldMatrix);\r\n\r\n        this._target.parent = this._parent;\r\n    }\r\n\r\n    /**\r\n     * Serializes the actions and its related information.\r\n     * @param parent defines the object to serialize in\r\n     * @returns the serialized object\r\n     */\r\n    public serialize(parent: any): any {\r\n        return super._serialize(\r\n            {\r\n                name: \"SetParentAction\",\r\n                properties: [Action._GetTargetProperty(this._target), Action._GetTargetProperty(this._parent)],\r\n            },\r\n            parent\r\n        );\r\n    }\r\n}\r\n\r\nRegisterClass(\"BABYLON.SetParentAction\", SetParentAction);\r\nRegisterClass(\"BABYLON.ExecuteCodeAction\", ExecuteCodeAction);\r\nRegisterClass(\"BABYLON.DoNothingAction\", DoNothingAction);\r\nRegisterClass(\"BABYLON.StopAnimationAction\", StopAnimationAction);\r\nRegisterClass(\"BABYLON.PlayAnimationAction\", PlayAnimationAction);\r\nRegisterClass(\"BABYLON.IncrementValueAction\", IncrementValueAction);\r\nRegisterClass(\"BABYLON.SetValueAction\", SetValueAction);\r\nRegisterClass(\"BABYLON.SetStateAction\", SetStateAction);\r\nRegisterClass(\"BABYLON.SetParentAction\", SetParentAction);\r\nRegisterClass(\"BABYLON.SwitchBooleanAction\", SwitchBooleanAction);\r\nRegisterClass(\"BABYLON.CombineAction\", CombineAction);\r\n"]}